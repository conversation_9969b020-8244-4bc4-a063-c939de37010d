stages:
  - prepare   # Prepare version number base on the source branch before building
  - build     # Compile the source code, generate artifacts
  - test      # Run unit tests, integration tests, and other automated tests
  - quality   # Perform linting, static code analysis, security checks, and other quality control measures
  - release   # Tag and version the release, push artifacts to repositories if needed
  - package   # Package the application for distribution (e.g., creating deployment bundles, and create and push docker images)
  - deploy    # Deploy the application to the target environment (development, staging, production, etc.)


variables:
  # Version management
  VERSION: "${CI_COMMIT_TAG#v}"
  # If not a tag, use branch name or short commit SHA
  VERSION_FALLBACK: "${CI_COMMIT_REF_SLUG}"
  NODE_VERSION: "20-alpine"
  CLIENT_DIR: "."
  PACKAGE_SOURCE: "msdeploy_package"

# Template for Node.js operations
.node_template:
  image: node:${NODE_VERSION}
  before_script:
    - yarn install --frozen-lockfile

.deploy_frontend_script_template:
  script:
    # Get version information
    - |
      $PACKAGE_NAME = "$env:PACKAGE_SOURCE.tar.gz"

      if (!(Test-Path -Path $PACKAGE_NAME)) {
        Write-Host "❌ Not found package $PACKAGE_NAME."
        exit 1
      }

      Write-Host "[INFO] Extracting deployment package."

      $SEVEN_ZIP_PATH = Join-Path $PWD "scripts\7za.exe"

      if (!(Test-Path -Path $SEVEN_ZIP_PATH)) {
          Write-Host "❌ 7za.exe not found at $SEVEN_ZIP_PATH" -ForegroundColor Red
          exit 1
      }

      & "$SEVEN_ZIP_PATH" x "${PACKAGE_NAME}" -aoa

      $PACKAGE_NAME = $PACKAGE_NAME -replace '.gz$', ''

      & "$SEVEN_ZIP_PATH" x "${PACKAGE_NAME}" -aoa

    - |
      $deployParams = @{
          source      = "$env:PACKAGE_SOURCE"
          siteName    = "$env:SITE_NAME"
          appPath     = "$env:APP_PATH"
          environment = "$env:ENVIRONMENT"
          user        = "$DEPLOY_USER"
          passwd      = "$DEPLOY_PWD"
          server      = "$env:DEPLOY_SERVER1"
          port        = $env:DEPLOY_SERVER_PORT1
      }

      & ".\scripts\ms_deploy.ps1" @deployParams

      if($env:DEPLOY_SERVER2 -and $env:DEPLOY_SERVER_PORT2) {
        $deployParams.server = $env:DEPLOY_SERVER2
        $deployParams.port = $env:DEPLOY_SERVER_PORT2

        & ".\scripts\ms_deploy.ps1" @deployParams
      }
