import {EventBusWrapper} from './event-bus';
import {IPlugin, IPluginContext} from './interface';

export abstract class BasePlugin implements IPlugin {
  protected _context?: IPluginContext;
  protected _eventBus?: EventBusWrapper;
  protected _destroyed = false;
  

  public initialize(context: IPluginContext): void {
    if (this._context) {
      throw new Error(`Plugin "${this.constructor.name}" is already initialized`);
    }
    this._context = context;
    this._eventBus = new EventBusWrapper(context.eventBus);
    this.onInitialize?.();
  }

  public destroy(): void {
    this._destroyed = true;
    this._context = undefined;
    this._eventBus?.destroy();
    this._eventBus = undefined;
  }

  public get destroyed(): boolean {
    return this._destroyed;
  }

  protected get context(): IPluginContext {
    if (!this._context) {
      throw new Error(`Plugin "${this.constructor.name}" is not initialized`);
    }
    return this._context;
  }

  protected get eventBus() {
    if (!this._eventBus) {
      throw new Error(`Plugin "${this.constructor.name}" is not initialized`);
    }
    return this._eventBus;
  }

  protected onInitialize?(): void;
} 