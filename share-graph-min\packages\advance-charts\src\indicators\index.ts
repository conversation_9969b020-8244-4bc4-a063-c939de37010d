import BBIndicator from "./bb-indicator";
import MACDIndicator from "./macd-indicator";
import RSIIndicator from "./rsi-indicator";
import VolumeIndicator from "./volume-indicator";
import {IndicatorFactory} from "./indicator-factory";
import SMAIndicator from "./sma-indicator";
import StochasticIndicator from "./stochastic-indicator";
import EMAIndicator from "./ema-indicator";
import WMAIndicator from "./wma-indicator";
import MomentumIndicator from "./momentum-indicator";
import WilliamsIndicator from "./williams-indicator";
import DMIIndicator from "./dmi-indicator";
import MassIndexIndicator from "./mass-index-indicator";
import UltimateOscillatorIndicator from "./ultimate-oscillator-indicator";

IndicatorFactory.registerIndicator('bb', BBIndicator)
IndicatorFactory.registerIndicator('rsi', RSIIndicator)
IndicatorFactory.registerIndicator('macd', MACDIndicator)
IndicatorFactory.registerIndicator('volume_overlay', VolumeIndicator, { overlay: true })
IndicatorFactory.registerIndicator('volume', VolumeIndicator)
IndicatorFactory.registerIndicator('sma', SMAIndicator)
IndicatorFactory.registerIndicator('stochastic', StochasticIndicator)
IndicatorFactory.registerIndicator('ema', EMAIndicator)
IndicatorFactory.registerIndicator('wma', WMAIndicator)
IndicatorFactory.registerIndicator('momentum', MomentumIndicator)
IndicatorFactory.registerIndicator('williams', WilliamsIndicator)
IndicatorFactory.registerIndicator('dmi', DMIIndicator)
IndicatorFactory.registerIndicator('massindex', MassIndexIndicator)
IndicatorFactory.registerIndicator('ultimateoscillator', UltimateOscillatorIndicator)

export { IndicatorFactory } from './indicator-factory'
export {default as BBIndicator} from "./bb-indicator";
export {default as MACDIndicator} from "./macd-indicator";
export {default as RSIIndicator} from "./rsi-indicator";
export {default as VolumeIndicator} from "./volume-indicator";
export {default as SMAIndicator} from "./sma-indicator";
export { default as StochasticIndicator } from "./stochastic-indicator";
export { default as EMAIndicator } from "./ema-indicator";
export { default as WMAIndicator } from "./wma-indicator";
export { default as MomentumIndicator } from "./momentum-indicator";
export { default as WilliamsIndicator } from "./williams-indicator";
export { default as DMIIndicator } from "./dmi-indicator";
export { default as MassIndexIndicator } from "./mass-index-indicator";
export { default as UltimateOscillatorIndicator } from "./ultimate-oscillator-indicator";
