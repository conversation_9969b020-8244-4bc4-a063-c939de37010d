.rule_gamma_template:
  environment:
    name: gamma
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v?\d+\.\d+\.\d+\-[a-zA-Z0-9\.\-]+$/'

.rule_release_branch:
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^release\/(\d+)?(\.\d+|\.x)?\.x$/'
    - if: '$CI_COMMIT_BRANCH == "master"'

build:release:
  extends:
    - .node_template
    - .rule_release_branch
  tags:
    - ee-v-docker2-dind
  stage: build
  script:
    - npm run build:prod
  artifacts:
    paths:
      - dist/
    expire_in: 1 week

test:release:
  extends:
    - .node_template
    - .rule_release_branch
  stage: test
  tags:
    - ee-v-docker2-dind
  needs:
    - build:release
  script:
    - npm run test

quality:release:
  extends:
    - .node_template
    - .rule_release_branch
  tags:
    - ee-v-docker2-dind
  stage: quality
  needs:
    - build:release
    - test:release
  script:
    - npm run lint

release:
  extends: .node_template
  tags:
    - ee-v-docker2-dind
  stage: release
  needs:
    - build:release
    - test:release
    - quality:release
  variables:
    PACKAGE_SOURCE: 'msdeploy_package'
  before_script:
    - apk add --no-cache git
    # Install tools needed for semantic-release
    - npm install @semantic-release/changelog@6.0.3 @semantic-release/commit-analyzer@10.0.4 @semantic-release/git@10.0.1 @semantic-release/exec@6.0.3 @semantic-release/gitlab@12.1.0 @semantic-release/release-notes-generator@11.0.7
    # - chmod +x ./scripts/update-versions-semantic.sh
    # - chmod +x ./scripts/package-client-semantic.sh
  script:
    - echo "Running semantic-release..."
    - npx semantic-release
  artifacts:
    paths:
      - CHANGELOG.md
      - version.json
      #- client_*.tar.gz
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
    - if: '$CI_COMMIT_BRANCH =~ /^release\/(\d+)?(\.\d+|\.x)?\.x$/'
      when: manual
