import i from "./bb-indicator.es.js";
import a from "./macd-indicator.es.js";
import m from "./rsi-indicator.es.js";
import o from "./volume-indicator.es.js";
import { IndicatorFactory as r } from "./indicator-factory.es.js";
import e from "./sma-indicator.es.js";
import c from "./stochastic-indicator.es.js";
import d from "./ema-indicator.es.js";
import n from "./wma-indicator.es.js";
import I from "./momentum-indicator.es.js";
import s from "./williams-indicator.es.js";
import g from "./dmi-indicator.es.js";
import p from "./mass-index-indicator.es.js";
import f from "./ultimate-oscillator-indicator.es.js";
import t from "./vroc-indicator.es.js";
r.registerIndicator("bb", i);
r.registerIndicator("rsi", m);
r.registerIndicator("macd", a);
r.registerIndicator("volume_overlay", o, { overlay: !0 });
r.registerIndicator("volume", o);
r.registerIndicator("sma", e);
r.registerIndicator("stochastic", c);
r.registerIndicator("ema", d);
r.registerIndicator("wma", n);
r.registerIndicator("momentum", I);
r.registerIndicator("williams", s);
r.registerIndicator("dmi", g);
r.registerIndicator("massindex", p);
r.registerIndicator("ultimateoscillator", f);
r.registerIndicator("vroc", t);
r.registerIndicator("vroc", t);
export {
  i as BBIndicator,
  g as DMIIndicator,
  d as EMAIndicator,
  r as IndicatorFactory,
  a as MACDIndicator,
  p as MassIndexIndicator,
  I as MomentumIndicator,
  m as RSIIndicator,
  e as SMAIndicator,
  c as StochasticIndicator,
  f as UltimateOscillatorIndicator,
  t as VROCIndicator,
  o as VolumeIndicator,
  n as WMAIndicator,
  s as WilliamsIndicator
};
//# sourceMappingURL=index.es.js.map
