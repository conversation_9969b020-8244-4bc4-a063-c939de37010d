import {DataChangedScope, Logical, Time} from "lightweight-charts";
import {binarySearchIndex, dayjs, Interval, mergeOhlcData, OHLCVSimple, Period, timeKey, timeToDayjs, timeToUnix} from "../../advance-charts";
import {BasePlugin} from "../core/base-plugin";
import {groupBy} from "es-toolkit";
import { IEventDefinition } from "../core/interface";
import {ChartCorePlugin} from "./chart-core-plugin";

export function roundTime(time: Time, period: Period) {
  switch (period) {
    case Period.minute:
    case Period.hour:
    case Period.day:
      return time;
    case Period.week:
      return dayjs.tz(timeToDayjs(time), 'UTC').startOf('week').unix() as Time;
    case Period.month:
      return dayjs.tz(timeToDayjs(time), 'UTC').startOf('month').unix() as Time;
    default:
      throw new Error(`Period : ${period} not support`)
  }
}

export const aggregate = (items: OHLCVSimple[]) => {
  const high = Math.max(...items.map(item => item.high))
  const low = Math.min(...items.map(item => item.low))
  const volume = items.reduce((acc, item) => acc + item.volume, 0)
  const open = items[0].open
  const close = items[items.length - 1].close
  return {
    time: items[0].time, 
    open,
    high,
    low,
    close,
    volume
  } satisfies OHLCVSimple
}

export type DataManagerPluginEvents = IEventDefinition<{
  dataSetChanged: DataChangedScope
}>

export class DataManagerPlugin extends BasePlugin {
  static name = 'data-manager-plugin';

  private _masterData: OHLCVSimple[] = [];
  private _dataSet: OHLCVSimple[] = [];
  private _dataSegment: {from: Time, to: Time, data: OHLCVSimple[]} | undefined;
  private _interval: Interval = { period: Period.day, times: 1 };

  get masterData() {
    return this._masterData;
  }

  get dataSet() {
    return this._dataSet;
  }

  get dataSegment() {
    const range = this.chartCore.chartApi.timeScale().getVisibleRange();
    if(!range) return [];
    if(this._dataSegment && this._dataSegment.from === range.from && this._dataSegment.to === range.to) return this._dataSegment.data;

    const { from, to } = range;
    const fromIndex = binarySearchIndex(this._dataSet, timeToUnix(from), (item) => timeToUnix(item.time));
    const toIndex = binarySearchIndex(this._dataSet, timeToUnix(to), (item) => timeToUnix(item.time));
    this._dataSegment = {
      from,
      to,
      data: this._dataSet.slice(fromIndex, toIndex + 1)
    };

    return this._dataSegment.data
  }

  get interval() {
    return this._interval;
  }

  private get chartCore() {
    return this.context.core.getPlugin<ChartCorePlugin>(ChartCorePlugin.name);
  }

  private get chartApi() {
    return this.chartCore.chartApi;
  }

  public getPointFromTime(time: Time) {
    const index = binarySearchIndex(this._dataSet, timeToUnix(time), (item) => timeToUnix(item.time));

    if(index === -1) return;
    return this.getPointFromIndex(index);
  }

  public getPointFromIndex(index: number) {
    const current = this._dataSet[index];
    const prev = this._dataSet[index > 0 ? index - 1 : index];

    return [current, prev] as const;
  }

  public lastPoint() {
    return this.getPointFromIndex(this._dataSet.length - 1);
  }

  public setInterval(interval: Interval) {
    this._interval = interval;
    this.generateDataSet()
    this.chartCore.tzDisplay.applyOptions({
      dataInterval: interval
    })
  }

  public setMasterData(data: OHLCVSimple[], interval: Interval) {
    this._masterData = data;
    this._interval = interval;
    this.generateDataSet()
    this.chartCore.tzDisplay.applyOptions({
      dataInterval: interval
    })
  }

  private generateDataSet() {
    const interval = this._interval

    try {
      if(interval.period === Period.day) this._dataSet = this._masterData
      if(interval.period === Period.hour) this._dataSet = this._masterData
      if(interval.period === Period.minute) this._dataSet = this._masterData

      const data = groupBy(this._masterData, item => timeKey(item.time, interval));

      this._dataSet = Object.entries(data)
        .map(([keyBy, items]) => [parseInt(keyBy), aggregate(items)] as const)
        .sort((a, b) => a[0] - b[0]).map(item => item[1]);
    } finally {
      this.eventBus.fire<DataManagerPluginEvents>({
        type: 'dataSetChanged',
        payload: 'full'
      })
    }
  }

  updateMasterData(data: OHLCVSimple[], forceUpdate: boolean = false) {
    const newData = mergeOhlcData(data, this.masterData);
    if (!forceUpdate && newData.length === this.masterData.length) return;

    this.setMasterData(newData, this.interval);

    return newData;
  }

  update(bar: OHLCVSimple, replaceLastPoint: boolean = false) {
    const [lastPoint] = this.lastPoint()
    const prepareBar = {
      ...bar,
      value: bar.close,
      time: replaceLastPoint ? lastPoint.time : bar.time
    }

    if(replaceLastPoint) {
      this.dataSet.splice(this.dataSet.length - 1, 1, prepareBar)
    } else {
      this.dataSet.push(prepareBar)

      const range = this.chartApi.timeScale().getVisibleLogicalRange();

      // keep current range when add new tick.
      if(range && (Math.floor(range.to) > this.dataSet.length)) {
        const newRange = {from: range.from, to: range.to}
        this.chartApi.timeScale().setVisibleLogicalRange(newRange)
      }
    }

    this.eventBus.fire<DataManagerPluginEvents>({
      type: 'dataSetChanged',
      payload: 'update'
    })
  }

  trade(trade: { time: Time, price: number, volume: number }) {
    const [lastPoint] = this.lastPoint();

    const currentKey = timeKey(lastPoint.time, this.interval)
    const newKey = timeKey(trade.time, this.interval)
    if(newKey < currentKey) {
      console.warn(`Trade timestamp ${newKey} is older than current ${currentKey}`)
      return;
    }
    if(currentKey === newKey) {
      this.update({
        open: lastPoint.open,
        high: Math.max(lastPoint.high, trade.price),
        low: Math.min(lastPoint.low, trade.price),
        close: trade.price,
        volume: lastPoint.volume + trade.volume,
        time: trade.time
      }, true)
    } else {
      this.update({
        open: trade.price,
        high: trade.price,
        low: trade.price,
        close: trade.price,
        volume: trade.volume,
        time: trade.time
      })
    }
  }

  fitContent() {
    const range = {from: 0 as Logical, to: Math.max(this.chartCore.maxBar, this._dataSet.length) as Logical};
    this.chartCore.fitRange(range);

    return range
  }
}