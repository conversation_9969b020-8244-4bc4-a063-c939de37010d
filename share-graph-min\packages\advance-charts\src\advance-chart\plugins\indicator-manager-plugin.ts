import {ChartIndicator, IGroupIndicatorByPane, IndicatorFactory} from "../../advance-charts";
import VolumeIndicator, {VolumeIndicatorOptions} from "../../indicators/volume-indicator";
import {BasePlugin} from "../core/base-plugin";
import {ChartTypePlugin, ChartTypePluginEvents} from "./chart-type-plugin";
import {ChartCorePlugin} from "./chart-core-plugin";
import {DataManagerPlugin, DataManagerPluginEvents} from "./data-manager-plugin";
import {IEventDefinition} from "../core/interface";
import {DataChangedScope} from "lightweight-charts";

export type IndicatorManagerPluginEvents = IEventDefinition<{
  indicatorChanged: { name: string, type: 'add' | 'remove' }
  volumeChanged: 'show' | 'hidden'
}>

export class IndicatorManagerPlugin extends BasePlugin {
  static name = 'indicator-manager-plugin';
  private _volumeType: 'volume' | 'volume_overlay' | undefined = undefined;
  private indicators = new Map<string, ChartIndicator>();

  private get chartType() {
    return this.context.core.getPlugin<ChartTypePlugin>(ChartTypePlugin.name);
  }

  get chartCore() {
    return this.context.core.getPlugin<ChartCorePlugin>(ChartCorePlugin.name);
  }

  get dataManager() {
    return this.context.core.getPlugin<DataManagerPlugin>(DataManagerPlugin.name);
  }

  protected onInitialize(): void {
    this.eventBus.subscribe<ChartTypePluginEvents>({
      type: 'chartTypeChanged',
      handler: () => {
        const mainSeries = this.chartType.mainSeries;
        if(!mainSeries) return;
        this.indicators.forEach(item => {
          item.mainSeriesChanged?.(mainSeries);
        })
      }
    });

    this.eventBus.subscribe<DataManagerPluginEvents>({
      type: 'dataSetChanged',
      handler: (dataScope: DataChangedScope) => {
        const dataSet = this.dataManager.dataSet;
        if(dataScope === 'full') {
          this.indicators.forEach(item => item.setData(dataSet))
        } else {
          this.indicators.forEach(item => item.update(dataSet))
        }
      }
    });
  }

  getIndicators() {
    return Array.from(this.indicators.values());
  }

  hasIndicator(name: string) {
    return this.indicators.has(name);
  }

  showVolume(type: typeof this._volumeType = 'volume_overlay', options?: Partial<VolumeIndicatorOptions>) {
    if (!this.chartType.mainSeries) return;
    if (!type) return;
    if (this.hasIndicator(type)) return;
    const indicator = this.addIndicator(type) as VolumeIndicator;
    if(options) indicator.applyOptions(options)
    if (this._volumeType !== type) {
      if (this._volumeType) this.removeIndicator(this._volumeType);
      this._volumeType = type;
    }
    const volumePaneIndex = indicator.getPaneIndex();
    if (volumePaneIndex === 0) return;
    const indicatorNeedToMove = Array.from(this.indicators.values()).map(
      (item) => {
        if (item === indicator) return;
        const paneIndex = item.getPaneIndex();
        if (paneIndex === 0) return;
        if (paneIndex >= volumePaneIndex) return;

        return {
          paneIndex,
          indicator: item,
        };
      }
    );
    indicator.setPaneIndex(1);
    
    indicatorNeedToMove.map((item) => {
      if (!item) return;
      item.indicator.setPaneIndex(item.paneIndex + 1);
    });
  }

  hiddenVolume() {
    if (!this._volumeType) return;
    this.removeIndicator(this._volumeType);
  }

  addIndicator(name: string) {
    const instance = IndicatorFactory.createIndicator(
      name,
      this.chartCore.chartApi,
      {
        numberFormatter: () => this.chartCore.numberFormatter,
        upColor: this.chartCore.options.upColor,
        downColor: this.chartCore.options.downColor,
      },
      this.chartCore.chartApi.panes().length
    );

    instance.setData(this.dataManager.dataSet);
    if(this.chartType.mainSeries) {
      instance.mainSeriesChanged?.(this.chartType.mainSeries);
    }

    this.indicators.set(name, instance);
    this.eventBus.fire<IndicatorManagerPluginEvents>({
      type: 'indicatorChanged',
      payload: {
        name,
        type: 'add'
      }
    })
    return instance;
  }

  removeIndicator(name: string) {
    const instance = this.indicators.get(name);
    if (!instance) return;
    instance.remove();
    this.indicators.delete(name);
    this.eventBus.fire<IndicatorManagerPluginEvents>({
      type: 'indicatorChanged',
      payload: {
        name,
        type: 'remove'
      }
    })
  }

  listIndicators() {
    return Array.from(this.indicators.keys()).filter(item => item !== this._volumeType);
  }

  groupIndicatorByPane(): Array<IGroupIndicatorByPane> {
    const indicators = Array.from(this.indicators.values());
    const panes = Array.from(this.chartCore.chartApi.panes()).map((pane) => ({
      pane,
      indicators: [] as ChartIndicator[],
    }));
    for (const indicator of indicators) {
      panes[indicator.getPaneIndex()].indicators.push(indicator);
    }

    return panes;
  }
}