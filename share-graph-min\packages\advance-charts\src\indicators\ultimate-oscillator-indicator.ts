import { IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time } from "lightweight-charts";
import { ChartIndicator, ChartIndicatorOptions } from "./abstract-indicator";
import { RegionPrimitive } from "../custom-primitive/primitive/region";
import { autoScaleInfoProviderCreator } from "../helpers/utils";
import { Context } from "../helpers/execution-indicator";

export interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  period1: number,
  period2: number,
  period3: number,
  weight1: number,
  weight2: number,
  weight3: number,
  priceLineColor: string,
  backgroundColor: string
}

export const defaultOptions: UltimateOscillatorIndicatorOptions = {
  color: "#3179f5",     // for Ultimate Oscillator
  priceLineColor: "#a1c3ff",
  backgroundColor: '#d2e0fa',
  period1: 7,      // Short period as per TradingView standard
  period2: 14,     // Medium period as per TradingView standard
  period3: 28,     // Long period as per TradingView standard
  weight1: 4,      // Weight for short period
  weight2: 2,      // Weight for medium period
  weight3: 1,      // Weight for long period
  overlay: false
}

export type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>

export type UltimateOscillatorData = [UltimateOscillatorLine]

export default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {
  ultimateOscillatorSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number) {
    super(chart, options)

    this.ultimateOscillatorSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'ultimateoscillator',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({ maxValue: 100, minValue: 0 })
    }, paneIndex);

    // Add region primitives for visual reference at 30 and 70 thresholds
    this.ultimateOscillatorSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 75,
        lowPrice: 70,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );

    this.ultimateOscillatorSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 30,
        lowPrice: 25,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): UltimateOscillatorIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): UltimateOscillatorData | undefined {
    const period1 = this.options.period1;
    const period2 = this.options.period2;
    const period3 = this.options.period3;
    const weight1 = this.options.weight1;
    const weight2 = this.options.weight2;
    const weight3 = this.options.weight3;

    const high = c.symbol.high;
    const low = c.symbol.low;
    const close = c.symbol.close;

    // Get MAX period needed first for proper series initialization
    const maxPeriod = Math.max(period1, period2, period3);

    // Create series to track previous close - need at least maxPeriod + 1 for lookback
    const closeSeries = c.new_var(close, maxPeriod + 1);
    if (!closeSeries.calculable()) return;

    // Get previous close (handle first bar case)
    const prevClose = closeSeries.get(1);

    // BP = Close - Min(Low, PrevClose)
    const minLowPrevClose = Math.min(low, prevClose);
    const buyingPressure = close - minLowPrevClose;
        
    // TR = Max(High, PrevClose) - Min(Low, PrevClose)  
    const maxHighPrevClose = Math.max(high, prevClose);
    const trueRange = maxHighPrevClose - minLowPrevClose;

    // Create series for BP and TR with proper period length
    const bpSeries = c.new_var(buyingPressure, maxPeriod);
    const trSeries = c.new_var(trueRange, maxPeriod);

    if (!bpSeries.calculable() || !trSeries.calculable()) return;

    // Check if we have enough data for the longest period
    if (!bpSeries.calculable() || !trSeries.calculable()) return;

    // Calculate period averages: Sum(BP) / Sum(TR) for each period
    const getPeriodAverage = (period: number) => {
        let bpSum = 0;
        let trSum = 0;
        
        // Sum the most recent 'period' values
        for (let i = 0; i < period; i++) {
            bpSum += bpSeries.get(i);
            trSum += trSeries.get(i);
        }
        
        return trSum > 0 ? bpSum / trSum : 0;
    };

    const avg1 = getPeriodAverage(period1);  // 7-period average
    const avg2 = getPeriodAverage(period2);  // 14-period average
    const avg3 = getPeriodAverage(period3);  // 28-period average

    // UO = 100 × [(4×Avg7) + (2×Avg14) + Avg28] / (4+2+1)
    const weightedSum = (weight1 * avg1) + (weight2 * avg2) + (weight3 * avg3);
    const totalWeight = weight1 + weight2 + weight3;
    
    // Apply the formula
    let ultimateOscillator = totalWeight > 0 ? 100 * (weightedSum / totalWeight) : 0;

    // Optional: Clamp to 0-100 range (TradingView doesn't explicitly clamp, but values should naturally stay in range)
    // Remove this if you want exact TradingView behavior
    // ultimateOscillator = Math.max(0, Math.min(100, ultimateOscillator));

    return [ultimateOscillator as UltimateOscillatorLine];
}

  applyIndicatorData() {
    const ultimateOscillatorData: SingleValueData[] = [];

    for (const bar of this._executionContext.data) {
      const value = bar.value;
      if (!value) continue;

      const time = bar.time as Time;
      ultimateOscillatorData.push({ time, value: value[0] });
    }

    this.ultimateOscillatorSeries.setData(ultimateOscillatorData);
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.ultimateOscillatorSeries);
  }

  _applyOptions() {
    this.ultimateOscillatorSeries.applyOptions({ color: this.options.color });
    this.applyIndicatorData();
  }

  setPaneIndex(paneIndex: number) {
    this.ultimateOscillatorSeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.ultimateOscillatorSeries.getPane().paneIndex();
  }
}
