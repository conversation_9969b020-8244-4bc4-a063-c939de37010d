import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {RegionPrimitive} from "../custom-primitive/primitive/region";
import {autoScaleInfoProviderCreator} from "../helpers/utils";
import {Context} from "../helpers/execution-indicator";

export interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  period1: number,
  period2: number,
  period3: number,
  weight1: number,
  weight2: number,
  weight3: number,
  priceLineColor: string,
  backgroundColor: string
}

export const defaultOptions: UltimateOscillatorIndicatorOptions = {
  color: "#3179f5",     // for Ultimate Oscillator
  priceLineColor: "#a1c3ff",
  backgroundColor: '#d2e0fa',
  period1: 7,      // Short period as per TradingView standard
  period2: 14,     // Medium period as per TradingView standard
  period3: 28,     // Long period as per TradingView standard
  weight1: 4,      // Weight for short period
  weight2: 2,      // Weight for medium period
  weight3: 1,      // Weight for long period
  overlay: false
}

export type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>

export type UltimateOscillatorData = [UltimateOscillatorLine]

export default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {
  ultimateOscillatorSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number) {
    super(chart, options)
    
    this.ultimateOscillatorSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'ultimateoscillator',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})
    }, paneIndex);
    
    // Add region primitives for visual reference at 30 and 70 thresholds
    this.ultimateOscillatorSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 75,
        lowPrice: 70,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
    
    this.ultimateOscillatorSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 30,
        lowPrice: 25,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): UltimateOscillatorIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): UltimateOscillatorData | undefined {
    const period1 = this.options.period1;
    const period2 = this.options.period2;
    const period3 = this.options.period3;
    const weight1 = this.options.weight1;
    const weight2 = this.options.weight2;
    const weight3 = this.options.weight3;

    const high = c.symbol.high;
    const low = c.symbol.low;
    const close = c.symbol.close;

    // We need enough data for the longest period calculation
    const maxPeriod = Math.max(period1, period2, period3);

    // Get previous close for True Range and Buying Pressure calculations
    const closeSeries = c.new_var(close, 2);
    if (!closeSeries.calculable()) {
      return;
    }
    const prevClose = closeSeries.get(1);

    // Step 1: Calculate Buying Pressure (BP) and True Range (TR)
    // BP = Close - Minimum(Low, Previous Close)
    // TR = Maximum(High, Previous Close) - Minimum(Low, Previous Close)

    const minLowPrevClose = Math.min(low, prevClose);
    const maxHighPrevClose = Math.max(high, prevClose);

    const buyingPressure = close - minLowPrevClose;
    const trueRange = maxHighPrevClose - minLowPrevClose;

    // Step 2: Maintain history for BP and TR calculations
    // Store BP and TR values for period calculations
    const bpSeries = c.new_var(buyingPressure, maxPeriod);
    const trSeries = c.new_var(trueRange, maxPeriod);

    if (!bpSeries.calculable() || !trSeries.calculable()) {
      return;
    }

    const bpValues = bpSeries.getAll();
    const trValues = trSeries.getAll();

    // Ensure we have enough data for the longest period
    if (bpValues.length < maxPeriod || trValues.length < maxPeriod) {
      return;
    }

    // Step 3: Calculate averages for each period
    // Average7 = (7-period BP Sum) / (7-period TR Sum)
    // Average14 = (14-period BP Sum) / (14-period TR Sum)
    // Average28 = (28-period BP Sum) / (28-period TR Sum)

    const bp1Sum = bpValues.slice(-period1).reduce((sum, val) => sum + val, 0);
    const tr1Sum = trValues.slice(-period1).reduce((sum, val) => sum + val, 0);
    const average1 = tr1Sum !== 0 ? bp1Sum / tr1Sum : 0;

    const bp2Sum = bpValues.slice(-period2).reduce((sum, val) => sum + val, 0);
    const tr2Sum = trValues.slice(-period2).reduce((sum, val) => sum + val, 0);
    const average2 = tr2Sum !== 0 ? bp2Sum / tr2Sum : 0;

    const bp3Sum = bpValues.slice(-period3).reduce((sum, val) => sum + val, 0);
    const tr3Sum = trValues.slice(-period3).reduce((sum, val) => sum + val, 0);
    const average3 = tr3Sum !== 0 ? bp3Sum / tr3Sum : 0;

    // Step 4: Calculate Ultimate Oscillator using exact TradingView/StockCharts formula
    // UO = 100 × [(4×Average7) + (2×Average14) + Average28] / (4+2+1)
    const weightedSum = (weight1 * average1) + (weight2 * average2) + (weight3 * average3);
    const totalWeight = weight1 + weight2 + weight3;
    const ultimateOscillator = totalWeight !== 0 ? (100 * weightedSum) / totalWeight : 0;

    return [ultimateOscillator as UltimateOscillatorLine];
  }

  applyIndicatorData() {
    const ultimateOscillatorData: SingleValueData[] = [];
    
    for(const bar of this._executionContext.data) {
      const value = bar.value;
      if(!value) continue;
      
      const time = bar.time as Time;
      ultimateOscillatorData.push({time, value: value[0]});
    }

    this.ultimateOscillatorSeries.setData(ultimateOscillatorData);
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.ultimateOscillatorSeries);
  }

  _applyOptions() {
    this.ultimateOscillatorSeries.applyOptions({color: this.options.color});
    this.applyIndicatorData();
  }

  setPaneIndex(paneIndex: number) {
    this.ultimateOscillatorSeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.ultimateOscillatorSeries.getPane().paneIndex();
  }
}
