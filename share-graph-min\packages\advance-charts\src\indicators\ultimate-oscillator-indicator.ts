import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {RegionPrimitive} from "../custom-primitive/primitive/region";
import {autoScaleInfoProviderCreator} from "../helpers/utils";
import {Context} from "../helpers/execution-indicator";

export interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  period1: number,
  period2: number,
  period3: number,
  weight1: number,
  weight2: number,
  weight3: number,
  priceLineColor: string,
  backgroundColor: string
}

export const defaultOptions: UltimateOscillatorIndicatorOptions = {
  color: "#3179f5",     // for Ultimate Oscillator
  priceLineColor: "#a1c3ff",
  backgroundColor: '#d2e0fa',
  period1: 7,      // Short period as per TradingView standard
  period2: 14,     // Medium period as per TradingView standard
  period3: 28,     // Long period as per TradingView standard
  weight1: 4,      // Weight for short period
  weight2: 2,      // Weight for medium period
  weight3: 1,      // Weight for long period
  overlay: false
}

export type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>

export type UltimateOscillatorData = [UltimateOscillatorLine]

export default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {
  ultimateOscillatorSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number) {
    super(chart, options)
    
    this.ultimateOscillatorSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'ultimateoscillator',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})
    }, paneIndex);
    
    // Add region primitives for visual reference at 30 and 70 thresholds
    this.ultimateOscillatorSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 75,
        lowPrice: 70,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
    
    this.ultimateOscillatorSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 30,
        lowPrice: 25,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): UltimateOscillatorIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): UltimateOscillatorData | undefined {
  const { period1, period2, period3, weight1, weight2, weight3 } = this.options;

  const high = c.symbol.high;
  const low = c.symbol.low;
  const close = c.symbol.close;

  const maxPeriod = Math.max(period1, period2, period3);

  // Lấy dữ liệu giá cần thiết
  const highSeries = c.new_var(high, maxPeriod + 1);
  const lowSeries = c.new_var(low, maxPeriod + 1);
  const closeSeries = c.new_var(close, maxPeriod + 1);

  if (!highSeries.calculable() || !lowSeries.calculable() || !closeSeries.calculable()) {
    return;
  }

  const bpSeries: number[] = [];
  const trSeries: number[] = [];

  for (let i = 1; i <= maxPeriod; i++) {
    const currentHigh = highSeries.get(i);
    const currentLow = lowSeries.get(i);
    const currentClose = closeSeries.get(i);
    const prevClose = closeSeries.get(i - 1);

    const minLowPrevClose = Math.min(currentLow, prevClose);
    const maxHighPrevClose = Math.max(currentHigh, prevClose);

    const bp = currentClose - minLowPrevClose;
    const tr = maxHighPrevClose - minLowPrevClose;

    bpSeries.push(bp);
    trSeries.push(tr);
  }

  const sum = (arr: number[], period: number) =>
    arr.slice(-period).reduce((acc, val) => acc + val, 0);

  const avg1 = sum(bpSeries, period1) / sum(trSeries, period1);
  const avg2 = sum(bpSeries, period2) / sum(trSeries, period2);
  const avg3 = sum(bpSeries, period3) / sum(trSeries, period3);

  const totalWeight = weight1 + weight2 + weight3;
  const uo = 100 * ((weight1 * avg1 + weight2 * avg2 + weight3 * avg3) / totalWeight);

  return [uo as UltimateOscillatorLine];
}


  applyIndicatorData() {
    const ultimateOscillatorData: SingleValueData[] = [];
    
    for(const bar of this._executionContext.data) {
      const value = bar.value;
      if(!value) continue;
      
      const time = bar.time as Time;
      ultimateOscillatorData.push({time, value: value[0]});
    }

    this.ultimateOscillatorSeries.setData(ultimateOscillatorData);
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.ultimateOscillatorSeries);
  }

  _applyOptions() {
    this.ultimateOscillatorSeries.applyOptions({color: this.options.color});
    this.applyIndicatorData();
  }

  setPaneIndex(paneIndex: number) {
    this.ultimateOscillatorSeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.ultimateOscillatorSeries.getPane().paneIndex();
  }
}
