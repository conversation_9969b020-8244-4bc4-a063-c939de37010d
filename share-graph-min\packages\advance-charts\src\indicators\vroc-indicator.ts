import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {RegionPrimitive} from "../custom-primitive/primitive/region";
import {autoScaleInfoProviderCreator} from "../helpers/utils";
import {Context} from "../helpers/execution-indicator";

export interface VROCIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  period: number,
  priceLineColor: string,
  backgroundColor: string
}

export const defaultOptions: VROCIndicatorOptions = {
  color: "rgba(255, 152, 0, 1)",     // Orange for VROC
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: '#ff98001a',
  period: 14,      // Default period for VROC
  overlay: false
}

export type VROCLine = Nominal<number, 'VROC'>

export type VROCData = [VROCLine]

export default class VROCIndicator extends ChartIndicator<VROCIndicatorOptions, VROCData> {
  vrocSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<VROCIndicatorOptions>, paneIndex?: number) {
    super(chart, options)
    
    this.vrocSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'vroc',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({minValue: -100, maxValue: 100})
    }, paneIndex);
    
    // Add zero line for reference
    this.vrocSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 5,
        lowPrice: -5,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): VROCIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): VROCData | undefined {
    const period = this.options.period;
    const volume = c.symbol.volume;

    // We need enough data for the period calculation plus current volume
    const volumeSeries = c.new_var(volume, period + 1);

    if (!volumeSeries.calculable()) {
        return;
    }

    // Get current volume (index 0) and volume from 'period' periods ago (index period)
    const currentVolume = volumeSeries.get(0);
    const pastVolume = volumeSeries.get(period);

    // Calculate Volume Rate of Change
    // VROC = ((Current Volume - Past Volume) / Past Volume) * 100
    if (pastVolume === 0) {
        return [0 as VROCLine]; // Avoid division by zero
    }

    const vroc = ((currentVolume - pastVolume) / pastVolume) * 100;

    return [vroc as VROCLine];
}

  applyIndicatorData() {
    const vrocData: SingleValueData[] = [];
    
    for(const bar of this._executionContext.data) {
      const value = bar.value;
      if(!value) continue;
      
      const time = bar.time as Time;
      vrocData.push({time, value: value[0]});
    }

    this.vrocSeries.setData(vrocData);
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.vrocSeries);
  }

  _applyOptions() {
    this.vrocSeries.applyOptions({color: this.options.color});
    this.applyIndicatorData();
  }

  setPaneIndex(paneIndex: number) {
    this.vrocSeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.vrocSeries.getPane().paneIndex();
  }
}
