{"version": 3, "file": "mass-index-indicator.cjs.js", "sources": ["../../src/indicators/mass-index-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface MassIndexIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  emaPeriod: number,\n  sumPeriod: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: MassIndexIndicatorOptions = {\n  color: \"#3179f5\",     // for Mass Index\n  priceLineColor: \"#a1c3ff\",\n  backgroundColor: '#ff98001a',\n  emaPeriod: 9,      // 9-period EMA as per TradingView standard\n  sumPeriod: 25,     // 25-period sum as per TradingView standard\n  overlay: false\n}\n\nexport type MassIndexLine = Nominal<number, 'MassIndex'>\n\nexport type MassIndexData = [MassIndexLine]\n\nexport default class MassIndexIndicator extends ChartIndicator<MassIndexIndicatorOptions, MassIndexData> {\n  massIndexSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<MassIndexIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.massIndexSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'massindex',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 30, minValue: 20})\n    }, paneIndex);\n    \n    // Add region primitive for visual reference at 27 threshold\n    this.massIndexSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 27.5,\n        lowPrice: 26.5,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): MassIndexIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): MassIndexData | undefined {\n    const emaPeriod = this.options.emaPeriod;\n    const sumPeriod = this.options.sumPeriod;\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n\n    // Calculate High-Low range\n    const range = high - low;\n\n    // Use custom EMA formula with alpha = 2/(n+1) as established in codebase\n    const emaAlpha = 2 / (emaPeriod + 1);\n\n    // Step 1: Calculate first EMA of (High-Low) range\n    // Follow the same pattern as EMA indicator\n    const rangeSeries = c.new_var(range, emaPeriod);\n    const firstEMAVar = c.new_var(NaN, 2);\n\n    if (!rangeSeries.calculable()) return;\n\n    const prevFirstEMA = firstEMAVar.get(1);\n    let currentFirstEMA: number;\n\n    if (isNaN(prevFirstEMA)) {\n      // Initialize first EMA with simple average of range values\n      const ranges = rangeSeries.getAll();\n      currentFirstEMA = ranges.reduce((sum, val) => sum + val, 0) / emaPeriod;\n    } else {\n      // EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]\n      currentFirstEMA = emaAlpha * range + (1 - emaAlpha) * prevFirstEMA;\n    }\n\n    firstEMAVar.set(currentFirstEMA);\n\n    // Step 2: Calculate second EMA of the first EMA\n    // Follow the same pattern as EMA indicator\n    const firstEMASeries = c.new_var(currentFirstEMA, emaPeriod);\n    const secondEMAVar = c.new_var(NaN, 2);\n\n    if (!firstEMASeries.calculable()) return;\n\n    const prevSecondEMA = secondEMAVar.get(1);\n    let currentSecondEMA: number;\n\n    if (isNaN(prevSecondEMA)) {\n      // Initialize second EMA with simple average of first EMA values\n      const firstEMAValues = firstEMASeries.getAll();\n      currentSecondEMA = firstEMAValues.reduce((sum, val) => sum + val, 0) / emaPeriod;\n    } else {\n      // EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]\n      currentSecondEMA = emaAlpha * currentFirstEMA + (1 - emaAlpha) * prevSecondEMA;\n    }\n\n    secondEMAVar.set(currentSecondEMA);\n\n    // Step 3: Calculate the ratio (First EMA / Second EMA)\n    // Avoid division by zero and ensure reasonable ratio values\n    const ratio = currentSecondEMA !== 0 ? currentFirstEMA / currentSecondEMA : 1;\n\n    // Step 4: Calculate Mass Index as sum of ratios over sumPeriod\n    const ratioSeries = c.new_var(ratio, sumPeriod);\n\n    if (!ratioSeries.calculable()) return;\n\n    const ratioValues = ratioSeries.getAll();\n\n    // Sum all ratios to get Mass Index\n    const massIndex = ratioValues.reduce((sum, val) => sum + val, 0);\n\n    return [massIndex as MassIndexLine];\n  }\n\n  applyIndicatorData() {\n    const massIndexData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      massIndexData.push({time, value: value[0]});\n    }\n\n    this.massIndexSeries.setData(massIndexData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.massIndexSeries);\n  }\n\n  _applyOptions() {\n    this.massIndexSeries.applyOptions({color: this.options.color});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.massIndexSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.massIndexSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "MassIndexIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "emaPeriod", "sumPeriod", "high", "low", "range", "emaAlpha", "rangeSeries", "firstEMAVar", "prevFirstEMA", "currentFirstEMA", "sum", "val", "firstEMASeries", "secondEMAVar", "prevSecondEMA", "currentSecondEMA", "ratio", "ratioSeries", "massIndexData", "bar", "value", "time"], "mappings": "6bAcaA,EAA4C,CACvD,MAAO,UACP,eAAgB,UAChB,gBAAiB,YACjB,UAAW,EACX,UAAW,GACX,QAAS,EACX,EAMA,MAAqBC,UAA2BC,EAAAA,cAAyD,CAGvG,YAAYC,EAAkBC,EAA8CC,EAAoB,CAC9F,MAAMF,EAAOC,CAAO,EAHtBE,EAAA,wBAKO,KAAA,gBAAkBH,EAAM,UAAUI,EAAAA,WAAY,CACjD,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,YACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,GAAI,SAAU,EAAG,CAAA,GAC/EH,CAAS,EAGZ,KAAK,gBAAgB,gBACnB,IAAII,kBAAgB,CAClB,QAAS,KACT,SAAU,KACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAA+C,CACtC,OAAAT,CAAA,CAGT,QAAQU,EAAuC,CACvC,MAAAC,EAAY,KAAK,QAAQ,UACzBC,EAAY,KAAK,QAAQ,UACzBC,EAAOH,EAAE,OAAO,KAChBI,EAAMJ,EAAE,OAAO,IAGfK,EAAQF,EAAOC,EAGfE,EAAW,GAAKL,EAAY,GAI5BM,EAAcP,EAAE,QAAQK,EAAOJ,CAAS,EACxCO,EAAcR,EAAE,QAAQ,IAAK,CAAC,EAEhC,GAAA,CAACO,EAAY,aAAc,OAEzB,MAAAE,EAAeD,EAAY,IAAI,CAAC,EAClC,IAAAE,EAEA,MAAMD,CAAY,EAGFC,EADHH,EAAY,OAAO,EACT,OAAO,CAACI,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EAAIX,EAG5CS,EAAAJ,EAAWD,GAAS,EAAIC,GAAYG,EAGxDD,EAAY,IAAIE,CAAe,EAI/B,MAAMG,EAAiBb,EAAE,QAAQU,EAAiBT,CAAS,EACrDa,EAAed,EAAE,QAAQ,IAAK,CAAC,EAEjC,GAAA,CAACa,EAAe,aAAc,OAE5B,MAAAE,EAAgBD,EAAa,IAAI,CAAC,EACpC,IAAAE,EAEA,MAAMD,CAAa,EAGFC,EADIH,EAAe,OAAO,EACX,OAAO,CAACF,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EAAIX,EAGpDe,EAAAV,EAAWI,GAAmB,EAAIJ,GAAYS,EAGnED,EAAa,IAAIE,CAAgB,EAIjC,MAAMC,EAAQD,IAAqB,EAAIN,EAAkBM,EAAmB,EAGtEE,EAAclB,EAAE,QAAQiB,EAAOf,CAAS,EAE1C,OAACgB,EAAY,aAOV,CALaA,EAAY,OAAO,EAGT,OAAO,CAACP,EAAKC,IAAQD,EAAMC,EAAK,CAAC,CAE7B,EAPH,MAOG,CAGpC,oBAAqB,CACnB,MAAMO,EAAmC,CAAC,EAEhC,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MAClB,GAAG,CAACC,EAAO,SAEX,MAAMC,EAAOF,EAAI,KACjBD,EAAc,KAAK,CAAC,KAAAG,EAAM,MAAOD,EAAM,CAAC,EAAE,CAAA,CAGvC,KAAA,gBAAgB,QAAQF,CAAa,CAAA,CAG5C,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,eAAe,CAAA,CAG9C,eAAgB,CACd,KAAK,gBAAgB,aAAa,CAAC,MAAO,KAAK,QAAQ,MAAM,EAC7D,KAAK,mBAAmB,CAAA,CAG1B,aAAaxB,EAAmB,CACzB,KAAA,gBAAgB,WAAWA,CAAS,CAAA,CAG3C,cAAuB,CACrB,OAAO,KAAK,gBAAgB,QAAQ,EAAE,UAAU,CAAA,CAEpD"}