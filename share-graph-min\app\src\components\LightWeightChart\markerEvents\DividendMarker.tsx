import { useEffect, useMemo, useState } from 'react';
import { useChartContext } from '../context';
import { useAppStore } from '../../../stores/useAppStore';
import { CHART_EVENT_KEYS } from '../../../constants/chartConstant';
import { getFromAndToDateByDataFeed } from '../../../utils/marker';
import {DataManagerPlugin, DataManagerPluginEvents, dayjs} from '@sharegraph-mini/advance-charts';
import { Time } from 'lightweight-charts';
import EventPoint from './EventPoint';
import { IDividendEvent, IEventPoints } from '../../../types/events';
import DividendHandler from './utils/dividend-marker';
import {findNearestPosition} from './utils/findNearestPosition';

const DividendMarker = () => {
  const enableEvents = useAppStore((state) => state.enableEvents);
  const formatNumberInstance = useAppStore((state) => state.formatNumberInstance);

  const enableDividend = enableEvents[CHART_EVENT_KEYS.DIVIDEND.key];
  const { getChart } = useChartContext();
  const [points, setPoints] = useState<IEventPoints<IDividendEvent>[]>([]);

  const dividendInstance = useMemo(() => new DividendHandler(undefined, undefined), [])

  useEffect(() => {
    const chart = getChart();
    if(!chart) return;
    const dataFeed = chart.getPlugin<DataManagerPlugin>(DataManagerPlugin.name)

    const handle = () => {
      const chartData = dataFeed.masterData
      if (chartData.length === 0) return;
      const { fromDate, toDate } = getFromAndToDateByDataFeed(chartData);
      if (enableDividend) {
        dividendInstance.load(fromDate, toDate);
      }
    }

    handle()
    return chart.eventBus.subscribe<DataManagerPluginEvents>({
      type: 'dataSetChanged',
      handler: handle
    })
  }, [enableDividend, dividendInstance, getChart]);

  useEffect(() => {
    const chart = getChart();
    if (!chart) return;
    const advanceChartApi = chart.api();
    const chartApi = advanceChartApi.chartApi;
    const drawEarning = () => {
      const newPoints: IEventPoints<IDividendEvent>[] = [];
      const dividendData = dividendInstance.getData()
      const timeScale = chartApi.timeScale()
      const mainSeries = advanceChartApi.mainSeries;
      if(!mainSeries) return;
      dividendData.forEach((point) => {
        const dateTime = dayjs.tz(point.exDate, 'UTC').startOf('day').unix();
        
        const index = timeScale.timeToIndex(dateTime as Time, true)
        if(index === null) {
          console.warn('Can not find index for dividend point', point)
          return;
        }

        const xPosi = findNearestPosition(point.exDate, timeScale, mainSeries);
        if(xPosi === undefined) {
          console.warn('Can not find xPosition for divient point', point)
          return;
        }
        newPoints.push({
          xPosition: xPosi,
          metaData: point,
        });
      });

      setPoints(newPoints.filter(x => x.xPosition));
    };
    drawEarning();
    chartApi.timeScale().subscribeVisibleLogicalRangeChange(drawEarning);

    const load = () => {
      drawEarning()
    }

    dividendInstance.dataLoaded.subscribe(load);

    return () => {
      dividendInstance.dataLoaded.unsubscribe(load);
      chartApi
        .timeScale()
        .unsubscribeVisibleLogicalRangeChange(drawEarning);
    };
  }, [getChart, enableDividend, dividendInstance]);

  if (!enableDividend) return null;

  return (
    <>
      {points.map((point, index) => (
        <EventPoint xPosition={point.xPosition} key={index} type="dividend">
          <p className='dividend-row'>
            <span>Ex-date:</span> {dayjs.tz(point.metaData.exDate, 'UTC').format('MM/DD/YYYY')}
          </p>
          <p className='dividend-row'>
            <span>Amount:</span> {formatNumberInstance.decimal(point.metaData.grossDivAdj)} {point.metaData.currency}
          </p>
        </EventPoint>
      ))}
    </>
  );
};

export default DividendMarker;
