"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const o=require("./bb-indicator.cjs.js"),i=require("./macd-indicator.cjs.js"),e=require("./rsi-indicator.cjs.js"),r=require("./volume-indicator.cjs.js"),t=require("./indicator-factory.cjs.js"),c=require("./sma-indicator.cjs.js"),d=require("./stochastic-indicator.cjs.js"),n=require("./ema-indicator.cjs.js"),I=require("./wma-indicator.cjs.js"),u=require("./momentum-indicator.cjs.js"),l=require("./williams-indicator.cjs.js"),s=require("./dmi-indicator.cjs.js"),f=require("./mass-index-indicator.cjs.js"),m=require("./ultimate-oscillator-indicator.cjs.js"),a=require("./vroc-indicator.cjs.js");t.IndicatorFactory.registerIndicator("bb",o.default);t.IndicatorFactory.registerIndicator("rsi",e.default);t.IndicatorFactory.registerIndicator("macd",i.default);t.IndicatorFactory.registerIndicator("volume_overlay",r.default,{overlay:!0});t.IndicatorFactory.registerIndicator("volume",r.default);t.IndicatorFactory.registerIndicator("sma",c.default);t.IndicatorFactory.registerIndicator("stochastic",d.default);t.IndicatorFactory.registerIndicator("ema",n.default);t.IndicatorFactory.registerIndicator("wma",I.default);t.IndicatorFactory.registerIndicator("momentum",u.default);t.IndicatorFactory.registerIndicator("williams",l.default);t.IndicatorFactory.registerIndicator("dmi",s.default);t.IndicatorFactory.registerIndicator("massindex",f.default);t.IndicatorFactory.registerIndicator("ultimateoscillator",m.default);t.IndicatorFactory.registerIndicator("vroc",a.default);t.IndicatorFactory.registerIndicator("vroc",a.default);exports.BBIndicator=o.default;exports.MACDIndicator=i.default;exports.RSIIndicator=e.default;exports.VolumeIndicator=r.default;exports.IndicatorFactory=t.IndicatorFactory;exports.SMAIndicator=c.default;exports.StochasticIndicator=d.default;exports.EMAIndicator=n.default;exports.WMAIndicator=I.default;exports.MomentumIndicator=u.default;exports.WilliamsIndicator=l.default;exports.DMIIndicator=s.default;exports.MassIndexIndicator=f.default;exports.UltimateOscillatorIndicator=m.default;exports.VROCIndicator=a.default;
//# sourceMappingURL=index.cjs.js.map
