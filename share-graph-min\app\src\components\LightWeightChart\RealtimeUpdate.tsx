import {useEffect} from 'react'
import {useChartContext} from './context'
import {useAppStore} from '../../stores/useAppStore';
import {timeToDate} from "@sharegraph-mini/advance-charts";
import {Time} from 'lightweight-charts';

const RealtimeUpdate = () => {
  const { getChart } = useChartContext();

  useEffect(() => {
    const chart = getChart();
    if(!chart) return;
    
    return useAppStore.subscribe((state, prevState) => {
      const trade = state.trades[state.selectedInstrumentId]
      const prevTrade = prevState.trades[state.selectedInstrumentId]
      if(trade === prevTrade) return;
      const time = timeToDate(trade.date);
      time.setSeconds(0)
      const cloneTrade = {...trade, time: Math.floor(time.getTime() / 1000) as Time};
      chart.api().trade(cloneTrade)
    })
  }, [getChart])
  return null
}

export default RealtimeUpdate