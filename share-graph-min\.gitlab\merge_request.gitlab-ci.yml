.rule_merge_request: &rule_merge_request
  rules:
    - if: $CI_MERGE_REQUEST_TITLE =~ /^wip[:]/i
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"

build:merge_request:
  extends: .node_template
  tags:
    - vn-v-docker-dind
  stage: build
  script:
    - npm run build "--" --mode production
  <<: *rule_merge_request

test:merge_request:
  extends: .node_template
  tags:
    - vn-v-docker-dind
  stage: test
  needs:
    - build:merge_request
  script:
    - npm run test
  <<: *rule_merge_request

quality:merge_request:
  extends: .node_template
  tags:
    - vn-v-docker-dind
  stage: quality
  needs:
    - build:merge_request
    - test:merge_request
  script:
    - npm run lint
  <<: *rule_merge_request
