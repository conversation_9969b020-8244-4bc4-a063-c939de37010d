import { useEffect, useState } from 'react';
import { useChartContext } from './context';
import { DataManagerPluginEvents, IAdvanceChartOptions, OHLCVSimple } from '@sharegraph-mini/advance-charts';
import { MouseEventParams } from 'lightweight-charts';
const calcPercentChange = (point: OHLCVSimple, prePoint: OHLCVSimple, chartOptions: IAdvanceChartOptions) => {
  const change = point.close - prePoint.close;
  const changePercent = (change / prePoint.close) * 100;
  
  return {
    ...point,
    change,
    changePercent,
    color: change > 0 ? chartOptions.upColor : chartOptions.downColor,
  };
};

type IMainLegendData = OHLCVSimple & {
  change: number;
  changePercent: number;
  color: string;
};

const MainLegend = () => {
  const { getChart } = useChartContext();
  const [chartHoverData, chartHoverDataSet] = useState<
    IMainLegendData | undefined
  >();
  const [chartHovered, setChartHovered] = useState<boolean>(false);
  const [lastPoint, lastPointSet] = useState<IMainLegendData | undefined>();


  useEffect(() => {
    const advanceChart = getChart()
    if (!advanceChart) return;
    const advanceChartApi = advanceChart.api();
    const chartApi = advanceChartApi.chartApi;
    const handle = ({ time }: MouseEventParams) => {
      if(!time) {
        setChartHovered(false);
        return;
      } else {
        setChartHovered(true);
      }
      const data = advanceChartApi.getPointFromTime(time);
      if(!data) return;
      const [current, pre] = data;
      const options = advanceChartApi.options;
      chartHoverDataSet(calcPercentChange(current, pre, options));
    };

    chartApi.subscribeCrosshairMove(handle)
    // chart.crosshairMoved().subscribe(handle);
    // chart.chartHovered().subscribe(setChartHovered);
    // setChartHovered(chart.chartHovered().lastParams()?.[0]);

    const chartUpdate = () => {
      const [current, pre] = advanceChartApi.lastPoint();
      const options = advanceChartApi.options;
      if (current && pre) {
        lastPointSet(calcPercentChange(current, pre, options));
      }
    };

    const cleanup = advanceChart.eventBus.subscribe<DataManagerPluginEvents>({
      type: 'dataSetChanged',
      handler: chartUpdate
    })

    chartUpdate()
    return () => {
      cleanup()
      chartApi.unsubscribeCrosshairMove(handle);
    };
  }, [getChart]);

  const data = chartHovered ? chartHoverData : lastPoint;
  const advanceChart = getChart();
  

  if(!advanceChart) return null;
  const advanceChartApi = advanceChart.api();
  return data ? (
    <div className='main-legend'>
      <span>
        O <span style={{ color: data.color }}>{advanceChartApi.numberFormatter.decimal(data.open)}</span> H{' '}
        <span style={{ color: data.color }}>{advanceChartApi.numberFormatter.decimal(data.high)}</span> L{' '}
        <span style={{ color: data.color }}>{advanceChartApi.numberFormatter.decimal(data.low)}</span> C{' '}
        <span style={{ color: data.color }}>{advanceChartApi.numberFormatter.decimal(data.close)}</span>{' '}
        <span style={{ color: data.color }}>
          {advanceChartApi.numberFormatter.decimal(data.change)} ({advanceChartApi.numberFormatter.percent(data.changePercent / 100)})
        </span>
      </span>
    </div>
  ) : null;
};

export default MainLegend;
