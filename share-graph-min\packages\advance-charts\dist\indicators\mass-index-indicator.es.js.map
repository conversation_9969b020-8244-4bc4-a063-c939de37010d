{"version": 3, "file": "mass-index-indicator.es.js", "sources": ["../../src/indicators/mass-index-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface MassIndexIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  emaPeriod: number,\n  sumPeriod: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: MassIndexIndicatorOptions = {\n  color: \"rgba(255, 152, 0, 1)\",     // Orange for Mass Index\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\n  backgroundColor: '#ff98001a',\n  emaPeriod: 9,      // 9-period EMA as per TradingView standard\n  sumPeriod: 25,     // 25-period sum as per TradingView standard\n  overlay: false\n}\n\nexport type MassIndexLine = Nominal<number, 'MassIndex'>\n\nexport type MassIndexData = [MassIndexLine]\n\nexport default class MassIndexIndicator extends ChartIndicator<MassIndexIndicatorOptions, MassIndexData> {\n  massIndexSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<MassIndexIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.massIndexSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'massindex',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 30, minValue: 20})\n    }, paneIndex);\n    \n    // Add region primitive for visual reference at 27 threshold\n    this.massIndexSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 27.5,\n        lowPrice: 26.5,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): MassIndexIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): MassIndexData | undefined {\n    const emaPeriod = this.options.emaPeriod;\n    const sumPeriod = this.options.sumPeriod;\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n\n    // We need enough data for EMA calculations and sum period\n    // Need at least emaPeriod for first EMA, then emaPeriod more for second EMA, then sumPeriod for final sum\n    const minDataNeeded = emaPeriod * 2 + sumPeriod;\n\n    const highSeries = c.new_var(high, minDataNeeded);\n    const lowSeries = c.new_var(low, minDataNeeded);\n\n    if (!highSeries.calculable() || !lowSeries.calculable()) {\n      return;\n    }\n\n    // Step 1: Calculate High-Low range\n    const range = high - low;\n\n    // Use custom EMA formula with alpha = 2/(n+1) as established in codebase\n    const emaAlpha = 2 / (emaPeriod + 1);\n\n    // Step 2: Calculate first 9-period EMA of (High-Low)\n    const firstEMAVar = c.new_var(0, 2);\n    const prevFirstEMA = firstEMAVar.get(1);\n\n    let currentFirstEMA: number;\n\n    if (isNaN(prevFirstEMA)) {\n      // Initialize first EMA with current range value\n      currentFirstEMA = range;\n    } else {\n      // EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]\n      currentFirstEMA = emaAlpha * range + (1 - emaAlpha) * prevFirstEMA;\n    }\n\n    firstEMAVar.set(currentFirstEMA);\n\n    // Step 3: Calculate second 9-period EMA of the first EMA\n    const secondEMAVar = c.new_var(0, 2);\n    const prevSecondEMA = secondEMAVar.get(1);\n\n    let currentSecondEMA: number;\n\n    if (isNaN(prevSecondEMA)) {\n      // Initialize second EMA with current first EMA value\n      currentSecondEMA = currentFirstEMA;\n    } else {\n      // EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]\n      currentSecondEMA = emaAlpha * currentFirstEMA + (1 - emaAlpha) * prevSecondEMA;\n    }\n\n    secondEMAVar.set(currentSecondEMA);\n\n    // Step 4: Calculate the ratio (First EMA / Second EMA)\n    const ratio = currentSecondEMA !== 0 ? currentFirstEMA / currentSecondEMA : 1;\n\n    // Step 5: Calculate Mass Index as sum of ratios over sumPeriod\n    const ratioHistory = c.new_var(ratio, sumPeriod + 1);\n\n    if (!ratioHistory.calculable()) {\n      return;\n    }\n\n    const ratioValues = ratioHistory.getAll();\n    if (ratioValues.length < sumPeriod) {\n      return;\n    }\n\n    // Sum the last sumPeriod ratios to get Mass Index\n    const massIndex = ratioValues.slice(-sumPeriod).reduce((sum, val) => sum + val, 0);\n\n    return [massIndex as MassIndexLine];\n  }\n\n  applyIndicatorData() {\n    const massIndexData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      massIndexData.push({time, value: value[0]});\n    }\n\n    this.massIndexSeries.setData(massIndexData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.massIndexSeries);\n  }\n\n  _applyOptions() {\n    this.massIndexSeries.applyOptions({color: this.options.color});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.massIndexSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.massIndexSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "MassIndexIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "emaPeriod", "sumPeriod", "high", "low", "minData<PERSON><PERSON>ed", "highSeries", "lowSeries", "range", "emaAlpha", "firstEMAVar", "prevFirstEMA", "currentFirstEMA", "secondEMAVar", "prevSecondEMA", "currentSecondEMA", "ratio", "ratioHistory", "ratioValues", "sum", "val", "massIndexData", "bar", "value", "time"], "mappings": ";;;;;;;AAcO,MAAMA,IAA4C;AAAA,EACvD,OAAO;AAAA;AAAA,EACP,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA;AAAA,EACX,WAAW;AAAA;AAAA,EACX,SAAS;AACX;AAMA,MAAqBC,UAA2BC,EAAyD;AAAA,EAGvG,YAAYC,GAAkBC,GAA8CC,GAAoB;AAC9F,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAKO,SAAA,kBAAkBH,EAAM,UAAUI,GAAY;AAAA,MACjD,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,IAAI,UAAU,GAAG,CAAA;AAAA,OAC/EH,CAAS,GAGZ,KAAK,gBAAgB;AAAA,MACnB,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAA+C;AACtC,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAuC;AACvC,UAAAC,IAAY,KAAK,QAAQ,WACzBC,IAAY,KAAK,QAAQ,WACzBC,IAAOH,EAAE,OAAO,MAChBI,IAAMJ,EAAE,OAAO,KAIfK,IAAgBJ,IAAY,IAAIC,GAEhCI,IAAaN,EAAE,QAAQG,GAAME,CAAa,GAC1CE,IAAYP,EAAE,QAAQI,GAAKC,CAAa;AAE9C,QAAI,CAACC,EAAW,WAAA,KAAgB,CAACC,EAAU;AACzC;AAIF,UAAMC,IAAQL,IAAOC,GAGfK,IAAW,KAAKR,IAAY,IAG5BS,IAAcV,EAAE,QAAQ,GAAG,CAAC,GAC5BW,IAAeD,EAAY,IAAI,CAAC;AAElC,QAAAE;AAEA,IAAA,MAAMD,CAAY,IAEFC,IAAAJ,IAGAI,IAAAH,IAAWD,KAAS,IAAIC,KAAYE,GAGxDD,EAAY,IAAIE,CAAe;AAG/B,UAAMC,IAAeb,EAAE,QAAQ,GAAG,CAAC,GAC7Bc,IAAgBD,EAAa,IAAI,CAAC;AAEpC,QAAAE;AAEA,IAAA,MAAMD,CAAa,IAEFC,IAAAH,IAGAG,IAAAN,IAAWG,KAAmB,IAAIH,KAAYK,GAGnED,EAAa,IAAIE,CAAgB;AAGjC,UAAMC,IAAQD,MAAqB,IAAIH,IAAkBG,IAAmB,GAGtEE,IAAejB,EAAE,QAAQgB,GAAOd,IAAY,CAAC;AAE/C,QAAA,CAACe,EAAa;AAChB;AAGI,UAAAC,IAAcD,EAAa,OAAO;AACpC,WAAAC,EAAY,SAAShB,IACvB,SAMK,CAFWgB,EAAY,MAAM,CAAChB,CAAS,EAAE,OAAO,CAACiB,GAAKC,MAAQD,IAAMC,GAAK,CAAC,CAE/C;AAAA,EAAA;AAAA,EAGpC,qBAAqB;AACnB,UAAMC,IAAmC,CAAC;AAEhC,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI;AAClB,UAAG,CAACC,EAAO;AAEX,YAAMC,IAAOF,EAAI;AACjB,MAAAD,EAAc,KAAK,EAAC,MAAAG,GAAM,OAAOD,EAAM,CAAC,GAAE;AAAA,IAAA;AAGvC,SAAA,gBAAgB,QAAQF,CAAa;AAAA,EAAA;AAAA,EAG5C,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,eAAe;AAAA,EAAA;AAAA,EAG9C,gBAAgB;AACd,SAAK,gBAAgB,aAAa,EAAC,OAAO,KAAK,QAAQ,OAAM,GAC7D,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,aAAa1B,GAAmB;AACzB,SAAA,gBAAgB,WAAWA,CAAS;AAAA,EAAA;AAAA,EAG3C,eAAuB;AACrB,WAAO,KAAK,gBAAgB,QAAQ,EAAE,UAAU;AAAA,EAAA;AAEpD;"}