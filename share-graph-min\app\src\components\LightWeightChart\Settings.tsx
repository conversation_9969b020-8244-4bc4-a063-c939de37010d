import { useEffect } from 'react';
import { useAppStore } from '../../stores/useAppStore';
import { useChartContext } from './context';
import { getChartRangeData } from '../../utils/common';
import {
  CHART_SETTING_KEYS,
  CHART_TYPE_KEYS,
} from '../../constants/chartConstant';
import { IndicatorFactory } from "@sharegraph-mini/advance-charts";
import { PriceScaleMode } from 'lightweight-charts';

const Settings = () => {
  const general = useAppStore((state) => state.appSettings.general);
  const { getChart } = useChartContext();
  const chartRange = useAppStore((state) => state.chartRange);

  const indicators = useAppStore((state) => state.indicators);
  const indicatorsOverlays = useAppStore((state) => state.overlays);
  const market = useAppStore(state => state.marketInfo?.marketManger)

  const chartType = useAppStore((state) => state.chartType);
  const chartSettings = useAppStore((state) => state.chartSettings);
  const volumeSettings = chartSettings.volume;
  const chartPreferences = chartSettings['chart-preferences'];
  const priceScaleMode = chartSettings['y-axis-preferences'];
  const lastPriceLine = chartSettings['show-last-close-line'];

  
  useEffect(() => {
    if(!market) return;
    const chart = getChart()
    if (!chart) return;
    const advanceChartApi = chart.api();
    const { from, to, interval } = getChartRangeData(chartRange, market);

    advanceChartApi.setRange({ from, to, interval });
  }, [chartRange, market, getChart]);

  useEffect(() => {
    const chart = getChart()
    if (!chart) return;
    const advanceChartApi = chart.api();
    switch (chartType) {
      case CHART_TYPE_KEYS.LINE.key:
        return advanceChartApi.setChartType('line');
      case CHART_TYPE_KEYS.CANDLESTICK.key:
        return advanceChartApi.setChartType('candle');
      case CHART_TYPE_KEYS.MOUNTAIN.key:
        return advanceChartApi.setChartType('mountain');
      case CHART_TYPE_KEYS.BASELINE.key:
        return advanceChartApi.setChartType('baseline');
      case CHART_TYPE_KEYS.BASE_MOUNTAIN.key:
        return advanceChartApi.setChartType('base-mountain');
      case CHART_TYPE_KEYS.BAR_OHLC.key:
        return advanceChartApi.setChartType('bar');
      default:
        return advanceChartApi.setChartType('line');
    }
  }, [chartType, getChart]);

  useEffect(() => {
    const chart = getChart()
    if (!chart) return;
    const advanceChartApi = chart.api();
    const allIndicators = indicators.concat(indicatorsOverlays);

    for (const indicator of advanceChartApi.listIndicators()) {
      if (allIndicators.includes(indicator)) continue;
      advanceChartApi.removeIndicator(indicator);
    }

    for (const indicator of allIndicators) {
      if (advanceChartApi.hasIndicator(indicator)) continue;
      if (!IndicatorFactory.indicatorRegistered(indicator)) {
        console.warn(`Indicator ${indicator} is not registered`);
        continue;
      }
      advanceChartApi.addIndicator(indicator);
    }
  }, [indicators, indicatorsOverlays, getChart]);

  useEffect(() => {
    const chart = getChart()
    if (!chart) return;
    const advanceChartApi = chart.api();
    const isShow = volumeSettings.includes(
      CHART_SETTING_KEYS.VOLUME.SHOW_HIDE.key
    );
    const volumeType = volumeSettings.includes(
      CHART_SETTING_KEYS.VOLUME.UNDERLAY.key
    )
      ? 'volume_overlay'
      : 'volume';

    const isSingleColor = !volumeSettings.includes(
      CHART_SETTING_KEYS.VOLUME.COLOR_VOLUME_BAR.key
    );
    advanceChartApi.hiddenVolume();
    
    if (isShow) {
      advanceChartApi.showVolume(
        volumeType,
        isSingleColor
          ? { upColor: general.primaryColor, downColor: general.primaryColor }
          : { upColor: general.upColor, downColor: general.downColor }
      );
    }
  }, [volumeSettings, getChart, general.primaryColor, general.upColor, general.downColor]);

  useEffect(() => {
    const chart = getChart()
    if (!chart) return;
    const advanceChartApi = chart.api();
    if (
      chartPreferences.includes(
        CHART_SETTING_KEYS.CHART_PREFERENCES.HIGH_LOW_VALUE.key
      )
    ) {
      advanceChartApi.applyOptions({ highLowLineVisible: true });
    } else {
      advanceChartApi.applyOptions({ highLowLineVisible: false });
    }

    switch (priceScaleMode[0]) {
      case CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.PERCENTAGE_VIEW.key:
        advanceChartApi.applyOptions({ priceScaleMode: PriceScaleMode.Percentage });
        break;
      case CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.LINEAR.key:
        advanceChartApi.applyOptions({ priceScaleMode: PriceScaleMode.Normal });
        break;
      case CHART_SETTING_KEYS.Y_AXIS_PREFERENCES.LOG_SCALE.key:
        advanceChartApi.applyOptions({ priceScaleMode: PriceScaleMode.Logarithmic });
        break;
      default:
        advanceChartApi.applyOptions({ priceScaleMode: PriceScaleMode.Normal });
        break;
    }

    if (lastPriceLine.includes(CHART_SETTING_KEYS.SHOW_LAST_CLOSE_LINE.key)) {
      advanceChartApi.applyOptions({ priceLineVisible: true });
    } else {
      advanceChartApi.applyOptions({ priceLineVisible: false });
    }
  }, [chartPreferences, priceScaleMode, lastPriceLine, getChart]);

  return null;
};

export default Settings;
