{"version": 3, "file": "dmi-indicator.es.js", "sources": ["../../src/indicators/dmi-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface DMIIndicatorOptions extends ChartIndicatorOptions {\n  adxColor: string,\n  plusDIColor: string,\n  minusDIColor: string,\n  period: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: DMIIndicatorOptions = {\n  adxColor: \"#f23645\",     // Orange for ADX\n  plusDIColor: \"#2962ff\",   // Green for +DI\n  minusDIColor: \"#ff6d00\",  // Red for -DI\n  priceLineColor: \"rgba(150, 150, 150, 0.35)\",\n  backgroundColor: '#ff98001a',\n  period: 14,\n  overlay: false\n}\n\nexport type ADXLine = Nominal<number, 'ADX'>\nexport type PlusDILine = Nominal<number, 'PlusDI'>\nexport type MinusDILine = Nominal<number, 'MinusDI'>\n\nexport type DMIData = [ADXLine, PlusDILine, MinusDILine]\n\nexport default class DMIIndicator extends ChartIndicator<DMIIndicatorOptions, DMIData> {\n  adxSeries: ISeriesApi<SeriesType>\n  plusDISeries: ISeriesApi<SeriesType>\n  minusDISeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<DMIIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.adxSeries = chart.addSeries(LineSeries, {\n      color: this.options.adxColor,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'dmi',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    this.plusDISeries = chart.addSeries(LineSeries, {\n      color: this.options.plusDIColor,\n      lineWidth: 1,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'dmi',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    this.minusDISeries = chart.addSeries(LineSeries, {\n      color: this.options.minusDIColor,\n      lineWidth: 1,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'dmi',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    this.adxSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 25,\n        lowPrice: 20,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): DMIIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): DMIData | undefined {\n    const period = this.options.period;\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n    const close = c.symbol.close;\n\n    const highSeries = c.new_var(high, period + 1);\n    const lowSeries = c.new_var(low, period + 1);\n    const closeSeries = c.new_var(close, period + 1);\n\n    if (!highSeries.calculable() || !lowSeries.calculable() || !closeSeries.calculable()) {\n      return;\n    }\n\n    const currentHigh = high;\n    const currentLow = low;\n    const prevHigh = highSeries.get(1);\n    const prevLow = lowSeries.get(1);\n    const prevClose = closeSeries.get(1);\n\n    // Step 1: Calculate +DM and -DM (Directional Movement)\n    const upMove = currentHigh - prevHigh;\n    const downMove = prevLow - currentLow;\n    \n    let plusDM = 0;\n    let minusDM = 0;\n    \n    if (upMove > downMove && upMove > 0) {\n      plusDM = upMove;\n    }\n    if (downMove > upMove && downMove > 0) {\n      minusDM = downMove;\n    }\n\n    // Step 2: Calculate True Range (TR)\n    const tr1 = currentHigh - currentLow;\n    const tr2 = Math.abs(currentHigh - prevClose);\n    const tr3 = Math.abs(currentLow - prevClose);\n    const trueRange = Math.max(tr1, tr2, tr3);\n\n    // Initialize EMA variables for smoothing\n    const alpha = 2 / (period + 1);\n    \n    // EMA variables for +DM, -DM, and TR\n    const plusDMEMA = c.new_var(plusDM, 2);\n    const minusDMEMA = c.new_var(minusDM, 2);\n    const trEMA = c.new_var(trueRange, 2);\n    \n    // Calculate smoothed values using EMA\n    const prevPlusDMEMA = plusDMEMA.get(1);\n    const prevMinusDMEMA = minusDMEMA.get(1);\n    const prevTREMA = trEMA.get(1);\n    \n    let smoothedPlusDM: number;\n    let smoothedMinusDM: number;\n    let smoothedTR: number;\n    \n    if (isNaN(prevPlusDMEMA) || isNaN(prevMinusDMEMA) || isNaN(prevTREMA)) {\n      // Initialize with simple averages for the first calculation\n      smoothedPlusDM = plusDM;\n      smoothedMinusDM = minusDM;\n      smoothedTR = trueRange;\n    } else {\n      // Use EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]\n      smoothedPlusDM = alpha * plusDM + (1 - alpha) * prevPlusDMEMA;\n      smoothedMinusDM = alpha * minusDM + (1 - alpha) * prevMinusDMEMA;\n      smoothedTR = alpha * trueRange + (1 - alpha) * prevTREMA;\n    }\n    \n    plusDMEMA.set(smoothedPlusDM);\n    minusDMEMA.set(smoothedMinusDM);\n    trEMA.set(smoothedTR);\n\n    // Step 3: Calculate +DI and -DI (Directional Indicators)\n    const plusDI = smoothedTR !== 0 ? (100 * smoothedPlusDM) / smoothedTR : 0;\n    const minusDI = smoothedTR !== 0 ? (100 * smoothedMinusDM) / smoothedTR : 0;\n\n    // Step 4: Calculate DX and ADX\n    const diSum = plusDI + minusDI;\n    const dx = diSum !== 0 ? (100 * Math.abs(plusDI - minusDI)) / diSum : 0;\n    \n    // EMA for ADX calculation\n    const adxEMA = c.new_var(dx, 2);\n    const prevADXEMA = adxEMA.get(1);\n    \n    let adx: number;\n    if (isNaN(prevADXEMA)) {\n      adx = dx;\n    } else {\n      adx = alpha * dx + (1 - alpha) * prevADXEMA;\n    }\n    \n    adxEMA.set(adx);\n\n    return [\n      adx as ADXLine,\n      plusDI as PlusDILine,\n      minusDI as MinusDILine\n    ];\n  }\n\n  applyIndicatorData() {\n    const adxData: SingleValueData[] = [];\n    const plusDIData: SingleValueData[] = [];\n    const minusDIData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      adxData.push({time, value: value[0]});\n      plusDIData.push({time, value: value[1]});\n      minusDIData.push({time, value: value[2]});\n    }\n\n    this.adxSeries.setData(adxData);\n    this.plusDISeries.setData(plusDIData);\n    this.minusDISeries.setData(minusDIData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.adxSeries);\n    this.chart.removeSeries(this.plusDISeries);\n    this.chart.removeSeries(this.minusDISeries);\n  }\n\n  _applyOptions() {\n    this.adxSeries.applyOptions({color: this.options.adxColor});\n    this.plusDISeries.applyOptions({color: this.options.plusDIColor});\n    this.minusDISeries.applyOptions({color: this.options.minusDIColor});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.adxSeries.moveToPane(paneIndex);\n    this.plusDISeries.moveToPane(paneIndex);\n    this.minusDISeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.adxSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "DMIIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period", "high", "low", "close", "highSeries", "lowSeries", "closeSeries", "currentHigh", "currentLow", "prevHigh", "prevLow", "prevClose", "upMove", "downMove", "plusDM", "minusDM", "tr1", "tr2", "tr3", "trueRang<PERSON>", "alpha", "plusDMEMA", "minusDMEMA", "trEMA", "prevPlusDMEMA", "prevMinusDMEMA", "prevTREMA", "smoothedPlusDM", "smoothedMinusDM", "smoothedTR", "plusDI", "minusDI", "diSum", "dx", "adxEMA", "prevADXEMA", "adx", "adxData", "plusDIData", "minusDIData", "bar", "value", "time"], "mappings": ";;;;;;;AAeO,MAAMA,IAAsC;AAAA,EACjD,UAAU;AAAA;AAAA,EACV,aAAa;AAAA;AAAA,EACb,cAAc;AAAA;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,SAAS;AACX;AAQA,MAAqBC,UAAqBC,EAA6C;AAAA,EAKrF,YAAYC,GAAkBC,GAAwCC,GAAoB;AACxF,UAAMF,GAAOC,CAAO;AALtB,IAAAE,EAAA;AACA,IAAAA,EAAA;AACA,IAAAA,EAAA;AAKO,SAAA,YAAYH,EAAM,UAAUI,GAAY;AAAA,MAC3C,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,KAAK,UAAU,EAAE,CAAA;AAAA,OAC/EH,CAAS,GAEP,KAAA,eAAeF,EAAM,UAAUI,GAAY;AAAA,MAC9C,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,KAAK,UAAU,EAAE,CAAA;AAAA,OAC/EH,CAAS,GAEP,KAAA,gBAAgBF,EAAM,UAAUI,GAAY;AAAA,MAC/C,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,KAAK,UAAU,EAAE,CAAA;AAAA,OAC/EH,CAAS,GAEZ,KAAK,UAAU;AAAA,MACb,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAAyC;AAChC,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAiC;AACjC,UAAAC,IAAS,KAAK,QAAQ,QACtBC,IAAOF,EAAE,OAAO,MAChBG,IAAMH,EAAE,OAAO,KACfI,IAAQJ,EAAE,OAAO,OAEjBK,IAAaL,EAAE,QAAQE,GAAMD,IAAS,CAAC,GACvCK,IAAYN,EAAE,QAAQG,GAAKF,IAAS,CAAC,GACrCM,IAAcP,EAAE,QAAQI,GAAOH,IAAS,CAAC;AAE3C,QAAA,CAACI,EAAW,WAAA,KAAgB,CAACC,EAAU,gBAAgB,CAACC,EAAY;AACtE;AAGF,UAAMC,IAAcN,GACdO,IAAaN,GACbO,IAAWL,EAAW,IAAI,CAAC,GAC3BM,IAAUL,EAAU,IAAI,CAAC,GACzBM,IAAYL,EAAY,IAAI,CAAC,GAG7BM,IAASL,IAAcE,GACvBI,IAAWH,IAAUF;AAE3B,QAAIM,IAAS,GACTC,IAAU;AAEV,IAAAH,IAASC,KAAYD,IAAS,MACvBE,IAAAF,IAEPC,IAAWD,KAAUC,IAAW,MACxBE,IAAAF;AAIZ,UAAMG,IAAMT,IAAcC,GACpBS,IAAM,KAAK,IAAIV,IAAcI,CAAS,GACtCO,IAAM,KAAK,IAAIV,IAAaG,CAAS,GACrCQ,IAAY,KAAK,IAAIH,GAAKC,GAAKC,CAAG,GAGlCE,IAAQ,KAAKpB,IAAS,IAGtBqB,IAAYtB,EAAE,QAAQe,GAAQ,CAAC,GAC/BQ,IAAavB,EAAE,QAAQgB,GAAS,CAAC,GACjCQ,IAAQxB,EAAE,QAAQoB,GAAW,CAAC,GAG9BK,IAAgBH,EAAU,IAAI,CAAC,GAC/BI,IAAiBH,EAAW,IAAI,CAAC,GACjCI,IAAYH,EAAM,IAAI,CAAC;AAEzB,QAAAI,GACAC,GACAC;AAEA,IAAA,MAAML,CAAa,KAAK,MAAMC,CAAc,KAAK,MAAMC,CAAS,KAEjDC,IAAAb,GACCc,IAAAb,GACLc,IAAAV,MAGIQ,IAAAP,IAAQN,KAAU,IAAIM,KAASI,GAC9BI,IAAAR,IAAQL,KAAW,IAAIK,KAASK,GACrCI,IAAAT,IAAQD,KAAa,IAAIC,KAASM,IAGjDL,EAAU,IAAIM,CAAc,GAC5BL,EAAW,IAAIM,CAAe,GAC9BL,EAAM,IAAIM,CAAU;AAGpB,UAAMC,IAASD,MAAe,IAAK,MAAMF,IAAkBE,IAAa,GAClEE,IAAUF,MAAe,IAAK,MAAMD,IAAmBC,IAAa,GAGpEG,IAAQF,IAASC,GACjBE,IAAKD,MAAU,IAAK,MAAM,KAAK,IAAIF,IAASC,CAAO,IAAKC,IAAQ,GAGhEE,IAASnC,EAAE,QAAQkC,GAAI,CAAC,GACxBE,IAAaD,EAAO,IAAI,CAAC;AAE3B,QAAAE;AACA,WAAA,MAAMD,CAAU,IACZC,IAAAH,IAEAG,IAAAhB,IAAQa,KAAM,IAAIb,KAASe,GAGnCD,EAAO,IAAIE,CAAG,GAEP;AAAA,MACLA;AAAA,MACAN;AAAA,MACAC;AAAA,IACF;AAAA,EAAA;AAAA,EAGF,qBAAqB;AACnB,UAAMM,IAA6B,CAAC,GAC9BC,IAAgC,CAAC,GACjCC,IAAiC,CAAC;AAE9B,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI;AAClB,UAAG,CAACC,EAAO;AAEX,YAAMC,IAAOF,EAAI;AACjB,MAAAH,EAAQ,KAAK,EAAC,MAAAK,GAAM,OAAOD,EAAM,CAAC,GAAE,GACpCH,EAAW,KAAK,EAAC,MAAAI,GAAM,OAAOD,EAAM,CAAC,GAAE,GACvCF,EAAY,KAAK,EAAC,MAAAG,GAAM,OAAOD,EAAM,CAAC,GAAE;AAAA,IAAA;AAGrC,SAAA,UAAU,QAAQJ,CAAO,GACzB,KAAA,aAAa,QAAQC,CAAU,GAC/B,KAAA,cAAc,QAAQC,CAAW;AAAA,EAAA;AAAA,EAGxC,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,SAAS,GACjC,KAAA,MAAM,aAAa,KAAK,YAAY,GACpC,KAAA,MAAM,aAAa,KAAK,aAAa;AAAA,EAAA;AAAA,EAG5C,gBAAgB;AACd,SAAK,UAAU,aAAa,EAAC,OAAO,KAAK,QAAQ,UAAS,GAC1D,KAAK,aAAa,aAAa,EAAC,OAAO,KAAK,QAAQ,aAAY,GAChE,KAAK,cAAc,aAAa,EAAC,OAAO,KAAK,QAAQ,cAAa,GAClE,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,aAAa7C,GAAmB;AACzB,SAAA,UAAU,WAAWA,CAAS,GAC9B,KAAA,aAAa,WAAWA,CAAS,GACjC,KAAA,cAAc,WAAWA,CAAS;AAAA,EAAA;AAAA,EAGzC,eAAuB;AACrB,WAAO,KAAK,UAAU,QAAQ,EAAE,UAAU;AAAA,EAAA;AAE9C;"}