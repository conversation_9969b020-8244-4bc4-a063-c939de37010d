# Mass Index Indicator Usage Guide

The Mass Index indicator is a technical analysis tool developed by <PERSON> that identifies potential trend reversals by analyzing the volatility of price ranges. It uses a double-smoothed exponential moving average of the High-Low range and sums the ratios over a specified period.

## Overview

The Mass Index indicator:
- Uses a 9-period EMA of the High-Low range
- Calculates a 9-period EMA of the first EMA result
- Computes ratios of the first EMA divided by the second EMA
- Sums these ratios over a 25-period lookback
- Values typically range from 20-30, with readings above 27 indicating potential reversal points

## Formula

```
1. First EMA = 9-period EMA of (High - Low)
2. Second EMA = 9-period EMA of First EMA
3. Ratio = First EMA / Second EMA
4. Mass Index = Sum of Ratios over 25 periods
```

## Usage Examples

### Basic Usage with IndicatorFactory

```typescript
import { AdvanceChart, IndicatorFactory } from '@sharegraph-mini/advance-charts';

// Create chart instance
const chart = new AdvanceChart(container);

// Add Mass Index indicator using factory
const massIndexIndicator = IndicatorFactory.createIndicator('massindex', chart, {
  emaPeriod: 9,     // EMA period for smoothing
  sumPeriod: 25,    // Period for summing ratios
  color: "rgba(255, 152, 0, 1)"  // Orange color
});

// Set data
massIndexIndicator.setData(ohlcvData);
```

### Direct Usage

```typescript
import { AdvanceChart, MassIndexIndicator } from '@sharegraph-mini/advance-charts';

// Create chart instance
const chart = new AdvanceChart(container);

// Create Mass Index indicator directly
const massIndexIndicator = new MassIndexIndicator(chart, {
  emaPeriod: 9,     // Custom EMA period
  sumPeriod: 25,    // Custom sum period
  color: "#FF9800"  // Custom color
});
```

### Custom Configuration

```typescript
const customMassIndexOptions = {
  emaPeriod: 9,
  sumPeriod: 25,
  color: "rgba(255, 152, 0, 1)",
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: '#ff98001a'
};

const massIndexIndicator = new MassIndexIndicator(chart, customMassIndexOptions);
```

## Data Format

The Mass Index indicator expects OHLCV data in the following format:

```typescript
const ohlcvData = [
  { time: '2023-01-01', open: 100, high: 105, low: 98, close: 103, volume: 1000 },
  { time: '2023-01-02', open: 103, high: 108, low: 101, close: 106, volume: 1200 },
  // ... more data points
];
```

## Output Values

The Mass Index indicator returns a single value for each data point:

```typescript
// MassIndexData = [MassIndex]
const lastPoint = massIndexIndicator.lastPoint();
if (lastPoint && lastPoint.value) {
  const [massIndex] = lastPoint.value;
  console.log(`Mass Index: ${massIndex}`);
}
```

## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `emaPeriod` | number | 9 | Period for EMA calculations |
| `sumPeriod` | number | 25 | Period for summing ratios |
| `color` | string | "rgba(255, 152, 0, 1)" | Line color |
| `priceLineColor` | string | "rgba(150, 150, 150, 0.35)" | Reference line color |
| `backgroundColor` | string | '#ff98001a' | Background color for reference region |
| `overlay` | boolean | false | Whether to display as overlay |

## Trading Signals

### Reversal Bulge Pattern
- **Signal**: Mass Index rises above 27 and then falls below 26.5
- **Interpretation**: Potential trend reversal incoming
- **Action**: Look for confirmation from other indicators

### High Volatility Warning
- **Signal**: Mass Index consistently above 27
- **Interpretation**: High volatility period
- **Action**: Exercise caution, expect increased price swings

## Visual Components

The Mass Index indicator creates:
- A single line series showing the Mass Index value
- A background region between 26.5-27.5 for visual reference
- Auto-scaling from 20-30 for optimal viewing
- Proper legend display showing the current value

## Integration with React

The Mass Index indicator includes a legend component for React applications:

```tsx
import MassIndexLegend from './legend/MassIndexLegend';

// In your component
<MassIndexLegend indicator={massIndexIndicator} advanceChart={advanceChart} />
```

## Mathematical Accuracy

The implementation follows TradingView's Mass Index calculation exactly:
- Uses custom EMA formula with alpha = 2/(n+1)
- Proper initialization and smoothing techniques
- Handles edge cases and insufficient data gracefully
- Mathematically accurate ratio calculations and summation

## Testing

The implementation includes comprehensive tests covering:
- Initialization with default and custom options
- Mathematical calculations with sample data
- Data application to chart series
- Cleanup and pane management
- Edge cases and error handling

## Best Practices

1. **Period Selection**: Use default periods (9 for EMA, 25 for sum) for standard analysis
2. **Confirmation**: Always use with other indicators for signal confirmation
3. **Market Context**: Most effective in trending markets
4. **Timeframe**: Works best on daily charts, can be adapted for other timeframes
5. **Risk Management**: Use proper stop-losses when trading reversal signals

## Example Implementation

```typescript
import { AdvanceChart, MassIndexIndicator } from '@sharegraph-mini/advance-charts';

class TradingStrategy {
  private chart: AdvanceChart;
  private massIndex: MassIndexIndicator;

  constructor(container: HTMLElement) {
    this.chart = new AdvanceChart(container);
    this.massIndex = new MassIndexIndicator(this.chart, {
      emaPeriod: 9,
      sumPeriod: 25,
      color: "#FF9800"
    });
  }

  checkReversalSignal(): boolean {
    const lastPoint = this.massIndex.lastPoint();
    if (!lastPoint?.value) return false;

    const [currentMI] = lastPoint.value;
    
    // Check for reversal bulge pattern
    return currentMI > 27; // Simplified check
  }

  setData(data: OHLCVData[]) {
    this.chart.setData(data);
    this.massIndex.setData(data);
  }
}
```

This implementation provides a mathematically accurate and feature-complete Mass Index indicator that matches TradingView's behavior and integrates seamlessly with the advance-charts framework.
