import i from "./bb-indicator.es.js";
import t from "./macd-indicator.es.js";
import m from "./rsi-indicator.es.js";
import o from "./volume-indicator.es.js";
import { IndicatorFactory as r } from "./indicator-factory.es.js";
import a from "./sma-indicator.es.js";
import e from "./stochastic-indicator.es.js";
import c from "./ema-indicator.es.js";
import d from "./wma-indicator.es.js";
import n from "./momentum-indicator.es.js";
import I from "./williams-indicator.es.js";
import s from "./dmi-indicator.es.js";
r.registerIndicator("bb", i);
r.registerIndicator("rsi", m);
r.registerIndicator("macd", t);
r.registerIndicator("volume_overlay", o, { overlay: !0 });
r.registerIndicator("volume", o);
r.registerIndicator("sma", a);
r.registerIndicator("stochastic", e);
r.registerIndicator("ema", c, { period: 14 });
r.registerIndicator("wma", d);
r.registerIndicator("momentum", n);
r.registerIndicator("williams", I);
r.registerIndicator("dmi", s);
export {
  i as BBIndicator,
  s as DMIIndicator,
  c as EMAIndicator,
  r as IndicatorFactory,
  t as MACDIndicator,
  n as MomentumIndicator,
  m as RSIIndicator,
  a as SMAIndicator,
  e as StochasticIndicator,
  o as VolumeIndicator,
  d as WMAIndicator,
  I as WilliamsIndicator
};
//# sourceMappingURL=index.es.js.map
