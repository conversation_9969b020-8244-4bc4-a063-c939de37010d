import o from "./bb-indicator.es.js";
import i from "./macd-indicator.es.js";
import m from "./rsi-indicator.es.js";
import t from "./volume-indicator.es.js";
import { IndicatorFactory as r } from "./indicator-factory.es.js";
import a from "./sma-indicator.es.js";
import e from "./stochastic-indicator.es.js";
import c from "./ema-indicator.es.js";
import d from "./wma-indicator.es.js";
import n from "./momentum-indicator.es.js";
import I from "./williams-indicator.es.js";
import s from "./dmi-indicator.es.js";
import l from "./mass-index-indicator.es.js";
import p from "./ultimate-oscillator-indicator.es.js";
r.registerIndicator("bb", o);
r.registerIndicator("rsi", m);
r.registerIndicator("macd", i);
r.registerIndicator("volume_overlay", t, { overlay: !0 });
r.registerIndicator("volume", t);
r.registerIndicator("sma", a);
r.registerIndicator("stochastic", e);
r.registerIndicator("ema", c);
r.registerIndicator("wma", d);
r.registerIndicator("momentum", n);
r.registerIndicator("williams", I);
r.registerIndicator("dmi", s);
r.registerIndicator("massindex", l);
r.registerIndicator("ultimateoscillator", p);
export {
  o as BBIndicator,
  s as DMIIndicator,
  c as EMAIndicator,
  r as IndicatorFactory,
  i as MACDIndicator,
  l as MassIndexIndicator,
  n as MomentumIndicator,
  m as RSIIndicator,
  a as SMAIndicator,
  e as StochasticIndicator,
  p as UltimateOscillatorIndicator,
  t as VolumeIndicator,
  d as WMAIndicator,
  I as WilliamsIndicator
};
//# sourceMappingURL=index.es.js.map
