/* eslint-disable react-refresh/only-export-components */
import {
  createContext,
  FC,
  ReactNode,
  useContext,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';
import { AdvanceChart } from '@sharegraph-mini/advance-charts';
import {
  IDataFetch,
} from '@sharegraph-mini/advance-charts';
import { DeepPartial } from 'lightweight-charts';
import { IAdvanceChartOptions } from '@sharegraph-mini/advance-charts';

export interface IChartContext {
  getChart: () => AdvanceChart | undefined;
}

export const chartContext = createContext({
  getChart: () => void 0,
  getDataFeed: () => void 0,
} as IChartContext);

export const ChartProvider: FC<{
  children?: ReactNode | ((container: ReactNode) => ReactNode);
  instanceDataFetch: IDataFetch;
  initializeChart?: (param: IChartContext) => void;
  options?: DeepPartial<IAdvanceChartOptions>;
}> = ({ instanceDataFetch, initializeChart, children, options }) => {
  const ref = useRef<HTMLDivElement>(null);

  const [chart, chartSet] = useState<IChartContext>({
    getChart: () => void 0,
  });

  useLayoutEffect(() => {
    const container = ref.current;
    if (!container) return;
    // const dataFeed = new DataFeed(chart, instanceDataFetch);
    const chart = new AdvanceChart(container,instanceDataFetch,  options);
    let destroyed = false;
    const context = {
      getChart: () => (destroyed ? undefined : chart)
    } satisfies IChartContext;

    chartSet(context);

    initializeChart?.(context);
    return () => {
      chart.remove();
      destroyed = true;
    };
  }, [initializeChart, instanceDataFetch, options]);

  const container = <div className="light-weight-chart-container" ref={ref} />;
  return (
    <chartContext.Provider value={chart}>
      {typeof children === 'function' ? (
        <div className="light-weight-chart">{children(container)}</div>
      ) : (
        <div className="light-weight-chart">
          {children}
          {container}
        </div>
      )}
    </chartContext.Provider>
  );
};
export const useChartContext = () => useContext(chartContext);

