import {useEffect, useState} from 'react';
import {useChartContext} from './context';
import {throttle} from 'es-toolkit';

const GoHome = () => {
  const { getChart } = useChartContext();
  const [show, showSet] = useState(false);

  useEffect(() => {
    const advanceChart = getChart()
    if(!advanceChart) return;
    const advanceChartApi = advanceChart.api();
    const timeScale = advanceChartApi.chartApi.timeScale();
    const handle = throttle(() => {
      const scrollPosition = Math.ceil(timeScale.scrollPosition())
      if(scrollPosition >= 0) {
        return showSet(false)
      } 

      showSet(true)
    }, 200)

    timeScale.subscribeVisibleTimeRangeChange(handle)
    return () => timeScale.unsubscribeVisibleTimeRangeChange(handle)
  }, [getChart])

  if(!show) return null;
  
  return (
    <button
      className="go-home-btn"
      onClick={() => getChart()?.api().chartApi.timeScale().scrollToPosition(1, false)}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 18 18"
        width={18}
        height={18}
      >
        <path
          fill="currentColor"
          d="M7.45 3.5 12.48 9l-5.03 5.49 1.1 1.01L14.52 9 8.55 2.49 7.45 3.5Z"
        />
        <path
          fill="currentColor"
          d="m3.93 5.99 2.58 3-2.58 3.02 1.14.98 3.42-4-3.42-3.98L3.93 6Z"
        />
      </svg>
    </button>
  );
};

export default GoHome;
