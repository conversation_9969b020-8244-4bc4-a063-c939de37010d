# Ultimate Oscillator Indicator Usage Guide

The Ultimate Oscillator indicator is a technical analysis tool developed by <PERSON> in 1976 that measures momentum across three different timeframes. It was designed to overcome the limitations of single-timeframe momentum oscillators by reducing false divergence signals through multi-timeframe analysis.

## Overview

The Ultimate Oscillator indicator:
- Uses three configurable timeframes (default: 7, 14, 28 periods)
- Calculates Buying Pressure and True Range for each timeframe
- Applies weighted averages to emphasize shorter timeframes
- Returns values in the 0-100 range
- Provides overbought (>70) and oversold (<30) signals
- Generates bullish and bearish divergence signals

## Formula

```
1. Buying Pressure (BP) = Close - Minimum(Low, Previous Close)
2. True Range (TR) = Maximum(High, Previous Close) - Minimum(Low, Previous Close)
3. Average7 = (7-period BP Sum) / (7-period TR Sum)
4. Average14 = (14-period BP Sum) / (14-period TR Sum)
5. Average28 = (28-period BP Sum) / (28-period TR Sum)
6. UO = 100 × [(4×Average7) + (2×Average14) + Average28] / (4+2+1)
```

## Usage Examples

### Basic Usage with IndicatorFactory

```typescript
import { AdvanceChart, IndicatorFactory } from '@sharegraph-mini/advance-charts';

// Create chart instance
const chart = new AdvanceChart(container);

// Add Ultimate Oscillator indicator using factory
const ultimateOscillatorIndicator = IndicatorFactory.createIndicator('ultimateoscillator', chart, {
  period1: 7,      // Short period
  period2: 14,     // Medium period
  period3: 28,     // Long period
  weight1: 4,      // Weight for short period
  weight2: 2,      // Weight for medium period
  weight3: 1,      // Weight for long period
  color: "rgba(255, 152, 0, 1)"  // Orange color
});

// Set data
ultimateOscillatorIndicator.setData(ohlcvData);
```

### Direct Usage

```typescript
import { AdvanceChart, UltimateOscillatorIndicator } from '@sharegraph-mini/advance-charts';

// Create chart instance
const chart = new AdvanceChart(container);

// Create Ultimate Oscillator indicator directly
const ultimateOscillatorIndicator = new UltimateOscillatorIndicator(chart, {
  period1: 7,      // Custom short period
  period2: 14,     // Custom medium period
  period3: 28,     // Custom long period
  weight1: 4,      // Custom weight for short period
  weight2: 2,      // Custom weight for medium period
  weight3: 1,      // Custom weight for long period
  color: "#FF9800" // Custom color
});
```

### Custom Configuration

```typescript
const customUltimateOscillatorOptions = {
  period1: 5,      // Faster short period
  period2: 10,     // Faster medium period
  period3: 20,     // Faster long period
  weight1: 6,      // Higher weight for short period
  weight2: 3,      // Higher weight for medium period
  weight3: 1,      // Same weight for long period
  color: "rgba(255, 152, 0, 1)",
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: '#ff98001a'
};

const ultimateOscillatorIndicator = new UltimateOscillatorIndicator(chart, customUltimateOscillatorOptions);
```

## Data Format

The Ultimate Oscillator indicator expects OHLCV data in the following format:

```typescript
const ohlcvData = [
  { time: '2023-01-01', open: 100, high: 105, low: 98, close: 103, volume: 1000 },
  { time: '2023-01-02', open: 103, high: 108, low: 101, close: 106, volume: 1200 },
  // ... more data points
];
```

## Output Values

The Ultimate Oscillator indicator returns a single value for each data point:

```typescript
// UltimateOscillatorData = [UltimateOscillator]
const lastPoint = ultimateOscillatorIndicator.lastPoint();
if (lastPoint && lastPoint.value) {
  const [ultimateOscillator] = lastPoint.value;
  console.log(`Ultimate Oscillator: ${ultimateOscillator}`);
}
```

## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `period1` | number | 7 | Short timeframe period |
| `period2` | number | 14 | Medium timeframe period |
| `period3` | number | 28 | Long timeframe period |
| `weight1` | number | 4 | Weight for short timeframe |
| `weight2` | number | 2 | Weight for medium timeframe |
| `weight3` | number | 1 | Weight for long timeframe |
| `color` | string | "rgba(255, 152, 0, 1)" | Line color |
| `priceLineColor` | string | "rgba(150, 150, 150, 0.35)" | Reference line color |
| `backgroundColor` | string | '#ff98001a' | Background color for reference regions |
| `overlay` | boolean | false | Whether to display as overlay |

## Trading Signals

### Bullish Divergence Signal
1. **Divergence Formation**: Price forms a lower low while Ultimate Oscillator makes a higher low
2. **Oversold Condition**: The low of the divergence should be below 30
3. **Confirmation**: Ultimate Oscillator breaks above the high of the divergence

### Bearish Divergence Signal
1. **Divergence Formation**: Price forms a higher high while Ultimate Oscillator makes a lower high
2. **Overbought Condition**: The high of the divergence should be above 70
3. **Confirmation**: Ultimate Oscillator falls below the low of the divergence

### Overbought/Oversold Levels
- **Overbought**: Values above 70 indicate potential selling opportunities
- **Oversold**: Values below 30 indicate potential buying opportunities
- **Neutral Zone**: Values between 30-70 indicate neutral momentum

## Visual Components

The Ultimate Oscillator indicator creates:
- A single line series showing the Ultimate Oscillator value (0-100 range)
- Background regions at 70-75 and 25-30 for visual reference
- Auto-scaling from 0-100 for optimal viewing
- Proper legend display showing the current value

## Integration with React

The Ultimate Oscillator indicator includes a legend component for React applications:

```tsx
import UltimateOscillatorLegend from './legend/UltimateOscillatorLegend';

// In your component
<UltimateOscillatorLegend indicator={ultimateOscillatorIndicator} advanceChart={advanceChart} />
```

## Mathematical Accuracy

The implementation follows TradingView's Ultimate Oscillator calculation exactly:
- **Buying Pressure (BP)**: `Close - Minimum(Low, Previous Close)`
- **True Range (TR)**: `Maximum(High, Previous Close) - Minimum(Low, Previous Close)`
- **Period Averages**: `(N-period BP Sum) / (N-period TR Sum)` for each timeframe
- **Weighted Formula**: `UO = 100 × [(4×Avg7) + (2×Avg14) + Avg28] / (4+2+1)`
- Handles edge cases and insufficient data gracefully
- Mathematically accurate multi-timeframe analysis matching TradingView exactly

## Best Practices

1. **Parameter Selection**: Use default periods (7, 14, 28) for standard analysis
2. **Divergence Confirmation**: Always wait for confirmation before acting on divergence signals
3. **Multiple Timeframes**: The indicator's strength lies in its multi-timeframe approach
4. **Risk Management**: Use proper stop-losses when trading divergence signals
5. **Combination Analysis**: Use with other indicators for signal confirmation

## Example Implementation

```typescript
import { AdvanceChart, UltimateOscillatorIndicator } from '@sharegraph-mini/advance-charts';

class TradingStrategy {
  private chart: AdvanceChart;
  private ultimateOscillator: UltimateOscillatorIndicator;

  constructor(container: HTMLElement) {
    this.chart = new AdvanceChart(container);
    this.ultimateOscillator = new UltimateOscillatorIndicator(this.chart, {
      period1: 7,
      period2: 14,
      period3: 28,
      weight1: 4,
      weight2: 2,
      weight3: 1,
      color: "#FF9800"
    });
  }

  checkOverboughtOversold(): 'overbought' | 'oversold' | 'neutral' {
    const lastPoint = this.ultimateOscillator.lastPoint();
    if (!lastPoint?.value) return 'neutral';

    const [currentUO] = lastPoint.value;
    
    if (currentUO > 70) return 'overbought';
    if (currentUO < 30) return 'oversold';
    return 'neutral';
  }

  setData(data: OHLCVData[]) {
    this.chart.setData(data);
    this.ultimateOscillator.setData(data);
  }
}
```

This implementation provides a mathematically accurate and feature-complete Ultimate Oscillator indicator that matches TradingView's behavior and integrates seamlessly with the advance-charts framework.
