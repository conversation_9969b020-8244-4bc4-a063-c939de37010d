"use strict";var W=Object.defineProperty;var j=(l,o,e)=>o in l?W(l,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[o]=e;var x=(l,o,e)=>j(l,typeof o!="symbol"?o+"":o,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const U=require("lightweight-charts"),z=require("./abstract-indicator.cjs.js"),I=require("../custom-primitive/primitive/region.cjs.js"),B=require("../helpers/utils.cjs.js"),y={color:"#3179f5",priceLineColor:"#a1c3ff",backgroundColor:"#d2e0fa",period1:7,period2:14,period3:28,weight1:4,weight2:2,weight3:1,overlay:!1};class E extends z.ChartIndicator{constructor(e,s,r){super(e,s);x(this,"ultimateOscillatorSeries");this.ultimateOscillatorSeries=e.addSeries(U.LineSeries,{color:this.options.color,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"ultimateoscillator",autoscaleInfoProvider:B.autoScaleInfoProviderCreator({maxValue:100,minValue:0})},r),this.ultimateOscillatorSeries.attachPrimitive(new I.RegionPrimitive({upPrice:75,lowPrice:70,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor})),this.ultimateOscillatorSeries.attachPrimitive(new I.RegionPrimitive({upPrice:30,lowPrice:25,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return y}formula(e){const s=this.options.period1,r=this.options.period2,a=this.options.period3,h=this.options.weight1,p=this.options.weight2,d=this.options.weight3,_=e.symbol.high,k=e.symbol.low,m=e.symbol.close,n=Math.max(s,r,a),g=e.new_var(m,2);if(!g.calculable())return;const v=g.get(1),S=Math.min(k,v),L=m-S,M=Math.max(_,v)-S,b=e.new_var(L,n),f=e.new_var(M,n);if(!b.calculable()||!f.calculable())return;const c=b.getAll(),u=f.getAll();if(c.length<n||u.length<n)return;const V=c.slice(-s).reduce((t,i)=>t+i,0),w=u.slice(-s).reduce((t,i)=>t+i,0),D=w!==0?V/w:0,q=c.slice(-r).reduce((t,i)=>t+i,0),P=u.slice(-r).reduce((t,i)=>t+i,0),R=P!==0?q/P:0,A=c.slice(-a).reduce((t,i)=>t+i,0),O=u.slice(-a).reduce((t,i)=>t+i,0),H=O!==0?A/O:0,T=h*D+p*R+d*H,C=h+p+d;return[C!==0?100*T/C:0]}applyIndicatorData(){const e=[];for(const s of this._executionContext.data){const r=s.value;if(!r)continue;const a=s.time;e.push({time:a,value:r[0]})}this.ultimateOscillatorSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.ultimateOscillatorSeries)}_applyOptions(){this.ultimateOscillatorSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.ultimateOscillatorSeries.moveToPane(e)}getPaneIndex(){return this.ultimateOscillatorSeries.getPane().paneIndex()}}exports.default=E;exports.defaultOptions=y;
//# sourceMappingURL=ultimate-oscillator-indicator.cjs.js.map
