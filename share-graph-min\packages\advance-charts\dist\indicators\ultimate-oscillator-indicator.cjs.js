"use strict";var R=Object.defineProperty;var T=(s,t,e)=>t in s?R(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e;var P=(s,t,e)=>T(s,typeof t!="symbol"?t+"":t,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const W=require("lightweight-charts"),j=require("./abstract-indicator.cjs.js"),O=require("../custom-primitive/primitive/region.cjs.js"),U=require("../helpers/utils.cjs.js"),x={color:"#3179f5",priceLineColor:"#a1c3ff",backgroundColor:"#d2e0fa",period1:7,period2:14,period3:28,weight1:4,weight2:2,weight3:1,overlay:!1};class z extends j.ChartIndicator{constructor(e,o,i){super(e,o);P(this,"ultimateOscillatorSeries");this.ultimateOscillatorSeries=e.addSeries(W.LineSeries,{color:this.options.color,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"ultimateoscillator",autoscaleInfoProvider:U.autoScaleInfoProviderCreator({maxValue:100,minValue:0})},i),this.ultimateOscillatorSeries.attachPrimitive(new O.RegionPrimitive({upPrice:75,lowPrice:70,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor})),this.ultimateOscillatorSeries.attachPrimitive(new O.RegionPrimitive({upPrice:30,lowPrice:25,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return x}formula(e){const{period1:o,period2:i,period3:l,weight1:g,weight2:v,weight3:S}=this.options,I=e.symbol.high,y=e.symbol.low,L=e.symbol.close,n=Math.max(o,i,l),f=e.new_var(I,n+1),b=e.new_var(y,n+1),h=e.new_var(L,n+1);if(!f.calculable()||!b.calculable()||!h.calculable())return;const c=[],u=[];for(let r=1;r<=n;r++){const p=f.get(r),d=b.get(r),m=h.get(r),w=h.get(r-1),C=Math.min(d,w),q=Math.max(p,w),V=m-C,H=q-C;c.push(V),u.push(H)}const a=(r,p)=>r.slice(-p).reduce((d,m)=>d+m,0),_=a(c,o)/a(u,o),k=a(c,i)/a(u,i),M=a(c,l)/a(u,l),D=g+v+S;return[100*((g*_+v*k+S*M)/D)]}applyIndicatorData(){const e=[];for(const o of this._executionContext.data){const i=o.value;if(!i)continue;const l=o.time;e.push({time:l,value:i[0]})}this.ultimateOscillatorSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.ultimateOscillatorSeries)}_applyOptions(){this.ultimateOscillatorSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.ultimateOscillatorSeries.moveToPane(e)}getPaneIndex(){return this.ultimateOscillatorSeries.getPane().paneIndex()}}exports.default=z;exports.defaultOptions=x;
//# sourceMappingURL=ultimate-oscillator-indicator.cjs.js.map
