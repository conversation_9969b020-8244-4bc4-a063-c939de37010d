"use strict";var H=Object.defineProperty;var U=(l,o,e)=>o in l?H(l,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[o]=e;var x=(l,o,e)=>U(l,typeof o!="symbol"?o+"":o,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const z=require("lightweight-charts"),B=require("./abstract-indicator.cjs.js"),I=require("../custom-primitive/primitive/region.cjs.js"),E=require("../helpers/utils.cjs.js"),y={color:"#3179f5",priceLineColor:"#a1c3ff",backgroundColor:"#d2e0fa",period1:7,period2:14,period3:28,weight1:4,weight2:2,weight3:1,overlay:!1};class F extends B.ChartIndicator{constructor(e,s,r){super(e,s);x(this,"ultimateOscillatorSeries");this.ultimateOscillatorSeries=e.addSeries(z.LineSeries,{color:this.options.color,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"ultimateoscillator",autoscaleInfoProvider:E.autoScaleInfoProviderCreator({maxValue:100,minValue:0})},r),this.ultimateOscillatorSeries.attachPrimitive(new I.RegionPrimitive({upPrice:75,lowPrice:70,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor})),this.ultimateOscillatorSeries.attachPrimitive(new I.RegionPrimitive({upPrice:30,lowPrice:25,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return y}formula(e){const s=this.options.period1,r=this.options.period2,a=this.options.period3,h=this.options.weight1,p=this.options.weight2,d=this.options.weight3,_=e.symbol.high,k=e.symbol.low,m=e.symbol.close,n=Math.max(s,r,a),g=e.new_var(m,2);if(!g.calculable())return;const S=g.get(1),v=Math.min(k,S),L=Math.max(_,S),M=m-v,V=L-v,b=e.new_var(M,n),f=e.new_var(V,n);if(!b.calculable()||!f.calculable())return;const c=b.getAll(),u=f.getAll();if(c.length<n||u.length<n)return;const D=c.slice(-s).reduce((t,i)=>t+i,0),w=u.slice(-s).reduce((t,i)=>t+i,0),q=w!==0?D/w:0,R=c.slice(-r).reduce((t,i)=>t+i,0),O=u.slice(-r).reduce((t,i)=>t+i,0),A=O!==0?R/O:0,T=c.slice(-a).reduce((t,i)=>t+i,0),P=u.slice(-a).reduce((t,i)=>t+i,0),W=P!==0?T/P:0,j=h*q+p*A+d*W,C=h+p+d;return[C!==0?100*j/C:0]}applyIndicatorData(){const e=[];for(const s of this._executionContext.data){const r=s.value;if(!r)continue;const a=s.time;e.push({time:a,value:r[0]})}this.ultimateOscillatorSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.ultimateOscillatorSeries)}_applyOptions(){this.ultimateOscillatorSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.ultimateOscillatorSeries.moveToPane(e)}getPaneIndex(){return this.ultimateOscillatorSeries.getPane().paneIndex()}}exports.default=F;exports.defaultOptions=y;
//# sourceMappingURL=ultimate-oscillator-indicator.cjs.js.map
