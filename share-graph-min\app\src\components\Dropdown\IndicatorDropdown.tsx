import Dropdown from './Dropdown';
import { DropdownMenuItem, OnChangeDropdown } from '../../types/common';
import { CHART_INDICATOR_KEYS } from '../../constants/chartConstant';
import { useAppStore } from '../../stores/useAppStore';
import { CHART_KEYS } from '../../constants/common';
import IndicatorIcon from '../../icons/Indicator';
import { i18n } from '@euroland/libs';
import { selectRadioIcon,  } from './common';



const IndicatorDropdown = () => {
  const indicators = useAppStore((state) => state.indicators);
  const setChartIndicators = useAppStore((state) => state.setChartIndicators);
  const value = {
    [CHART_KEYS.INDICATORS]: indicators,
  };

  const menuData: DropdownMenuItem[] = [
    {
      id: CHART_KEYS.INDICATORS,
      label: '',
      hideGroupLabel: true,
      items: [
        {
          id: CHART_INDICATOR_KEYS.MACD.key,
          label: CHART_INDICATOR_KEYS.MACD.label,
          ...selectRadioIcon,
          // icon: <RadioIcon />,
          // activeIcon: <RadioIcon active />,
        },
        {
          id: CHART_INDICATOR_KEYS.RSI.key,
          label: CHART_INDICATOR_KEYS.RSI.label,
          ...selectRadioIcon,
          // icon: <RadioIcon />,
          // activeIcon: <RadioIcon active />,
        },
        {
          id: CHART_INDICATOR_KEYS.STOCHASTIC.key,
          label: CHART_INDICATOR_KEYS.STOCHASTIC.label,
          ...selectRadioIcon,
          // icon: <RadioIcon />,
          // activeIcon: <RadioIcon active />,
        },
        {
          id: CHART_INDICATOR_KEYS.MM.key,
          label: CHART_INDICATOR_KEYS.MM.label,
          ...selectRadioIcon,
          // icon: <RadioIcon />,
          // activeIcon: <RadioIcon active />,
        },
        {
          id: CHART_INDICATOR_KEYS.WILLIAMS.key,
          label: CHART_INDICATOR_KEYS.WILLIAMS.label,
          ...selectRadioIcon,
          // icon: <RadioIcon />,
          // activeIcon: <RadioIcon active />,
        },
        {
          id: CHART_INDICATOR_KEYS.DMI.key,
          label: CHART_INDICATOR_KEYS.DMI.label,
          ...selectRadioIcon,
          // icon: <RadioIcon />,
          // activeIcon: <RadioIcon active />,
        },
        {
          id: CHART_INDICATOR_KEYS.MASS_INDEX.key,
          label: CHART_INDICATOR_KEYS.MASS_INDEX.label,
          ...selectRadioIcon,
          // icon: <RadioIcon />,
          // activeIcon: <RadioIcon active />,
        },
        {
          id: CHART_INDICATOR_KEYS.ULTIMATE_OSCILLATOR.key,
          label: CHART_INDICATOR_KEYS.ULTIMATE_OSCILLATOR.label,
          ...selectRadioIcon,
          // icon: <RadioIcon />,
          // activeIcon: <RadioIcon active />,
        },
      ],
    },
    
  ];

  const handleChange: OnChangeDropdown = (val, ) => {
    setChartIndicators(val);
  };

  return (
    <Dropdown
      menuData={menuData}
      onChange={handleChange}
      value={value}
      description={i18n.translate('technical-indicators')}
      placeholder={
        <>
          <IndicatorIcon /> <span>{i18n.translate("indicator")}</span>
        </>
      }
    />
  );
};

export default IndicatorDropdown;
