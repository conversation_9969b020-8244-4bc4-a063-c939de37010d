import { expect, it, vi } from 'vitest';
import { AdvanceChart } from './advance-chart';
import { Time } from 'lightweight-charts';
import { Period } from './i-advance-chart';
import {IDataFetch} from './plugins';

// Test helpers for cleaner, reusable test data
const createContainer = () => {
  const container = document.createElement('div');
  document.body.appendChild(container);
  return container;
};

const createOHLCVData = (additionalItems: Array<{ time: Time; open: number; high: number; low: number; close: number; volume: number }> = []) => [
  { time: 1 as Time, open: 10, high: 15, low: 5, close: 12, volume: 100 },
  { time: 2 as Time, open: 12, high: 18, low: 8, close: 16, volume: 200 },
  ...additionalItems
];

// Mock data fetch for testing
const createMockDataFetch = (): IDataFetch => ({
  fetchInitialData: vi.fn().mockResolvedValue([]),
  fetchPaginationData: vi.fn().mockResolvedValue([]),
  fetchUpdateData: vi.fn().mockResolvedValue([]),
});

// Core functionality: Chart initialization should provide working chart API when given container
it('should initialize with working chart API when given container', () => {
  const container = createContainer();
  const mockDataFetch = createMockDataFetch();
  const chart = new AdvanceChart(container, mockDataFetch);
  const api = chart.api();

  expect(api.chartApi).toBeDefined();
  expect(api.loading).toBeDefined();
});

// Core functionality: Chart type changes should be reflected in chart state
it('should update chart type when set to different types', () => {
  const container = createContainer();
  const mockDataFetch = createMockDataFetch();
  const chart = new AdvanceChart(container, mockDataFetch);
  const api = chart.api();

  api.setChartType('line');
  expect(api.chartType).toBe('line');

  api.setChartType('candle');
  expect(api.chartType).toBe('candle');

  api.setChartType('mountain');
  expect(api.chartType).toBe('mountain');
});

// Core functionality: Setting same chart type multiple times should work without issues
it('should handle setting the same chart type multiple times', () => {
  const container = createContainer();
  const mockDataFetch = createMockDataFetch();
  const chart = new AdvanceChart(container, mockDataFetch);
  const api = chart.api();

  api.setChartType('line');
  const firstTypeResult = api.chartType;

  api.setChartType('line');
  expect(api.chartType).toBe(firstTypeResult);
});

// Core functionality: Indicator management should work as expected
it('should add and remove indicators correctly', () => {
  const container = createContainer();
  const mockDataFetch = createMockDataFetch();
  const chart = new AdvanceChart(container, mockDataFetch);
  const api = chart.api();

  // Initially no indicators
  expect(api.hasIndicator('volume')).toBe(false);

  // Add indicator
  api.addIndicator('volume');
  expect(api.hasIndicator('volume')).toBe(true);

  // Add another indicator
  api.addIndicator('macd');
  expect(api.hasIndicator('macd')).toBe(true);
  expect(api.getIndicators().length).toBe(2);

  // Remove indicator
  api.removeIndicator('volume');
  expect(api.hasIndicator('volume')).toBe(false);
  expect(api.hasIndicator('macd')).toBe(true);
});

// Core functionality: Data setting should update chart data
it('should accept and store OHLCV data correctly', () => {
  const container = createContainer();
  const mockDataFetch = createMockDataFetch();
  const chart = new AdvanceChart(container, mockDataFetch);
  const api = chart.api();
  const testData = createOHLCVData();

  api.setMasterData(testData, { period: Period.day, times: 1 }); // ground by day = 86400 seconds

  const result = api.dataSet;
  expect(result).toHaveLength(1);
  expect(result[0]).toMatchObject({
    time: 1,
    open: 10,
    high: 18,
    low: 5,
    close: 16,
    volume: 300
  });
});

// Core functionality: Plugin management should work correctly
it('should allow plugin registration and management', () => {
  const container = createContainer();
  const mockDataFetch = createMockDataFetch();
  const chart = new AdvanceChart(container, mockDataFetch);

  // Test plugin interface exists
  expect(typeof chart.registerPlugin).toBe('function');
  expect(typeof chart.unregisterPlugin).toBe('function');
  expect(typeof chart.getPlugin).toBe('function');
});

// Core functionality: Event bus should be accessible
it('should provide access to event bus', () => {
  const container = createContainer();
  const mockDataFetch = createMockDataFetch();
  const chart = new AdvanceChart(container, mockDataFetch);

  expect(chart.eventBus).toBeDefined();
  expect(typeof chart.eventBus.subscribe).toBe('function');
  expect(typeof chart.eventBus.fire).toBe('function');
});

// Critical cleanup: Chart removal should clean up all resources
it('should clean up all resources when chart is destroyed', () => {
  const container = createContainer();
  const mockDataFetch = createMockDataFetch();
  const chart = new AdvanceChart(container, mockDataFetch);
  const api = chart.api();
  
  // Setup some indicators to verify cleanup
  api.addIndicator('volume');
  api.addIndicator('macd');
  
  // Subscribe to destruction event to verify it fires
  const destroyCallback = vi.fn();
  chart.eventBus.subscribe({
    type: 'CoreDestroying',
    handler: destroyCallback
  });

  // Destroy chart
  chart.destroy();

  // Verify cleanup - indicators should be cleaned up
  expect(destroyCallback).toHaveBeenCalled();
});

// Core functionality: Options can be applied after initialization
it('should accept options updates after initialization', () => {
  const container = createContainer();
  const mockDataFetch = createMockDataFetch();
  const chart = new AdvanceChart(container, mockDataFetch);

  // Should not throw when applying options
  expect(() => {
    chart.applyOptions({
      upColor: '#00ff00',
      downColor: '#ff0000',
      mainColor: '#0000ff'
    });
  }).not.toThrow();
}); 