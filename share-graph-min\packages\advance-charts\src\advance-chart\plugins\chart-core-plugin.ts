import {
  ChartOptions,
  createChart,
  DeepPartial,
  IChartApi,
  Logical,
  PriceScaleMode,
  TickMarkType,
  Time,
} from "lightweight-charts";
import { BasePlugin } from "../core/base-plugin";
import { IEventDefinition } from "../core/interface";
import { TZDisplay } from "../TZDisplay";
import { IAdvanceChartOptions } from "../i-advance-chart";
import { cloneDeep, merge } from "es-toolkit";
import { NumberFormatterFactory, timeToDate } from "../../helpers";
import { downColor, upColor } from "../../advance-charts";

export const defaultAdvanceChartOptions: IAdvanceChartOptions = {
  upColor: upColor,
  downColor: downColor,
  mainColor: "#3594e7",
  highLowLineVisible: true,
  highLineColor: upColor,
  lowLineColor: downColor,
  priceScaleMode: PriceScaleMode.Normal,
  priceLineVisible: false,
  locale: "en",
  gridColor: "#f2f2f2",
  axesColor: "#333",
  tzDisplay: Intl.DateTimeFormat().resolvedOptions().timeZone,
  height: 500,
};

export type ChartCorePluginEvents = IEventDefinition<{
  chartOptionsChanged: [Partial<IAdvanceChartOptions>, IAdvanceChartOptions]
}>

export class ChartCorePlugin extends BasePlugin {
  static name = "chart-core-plugin";

  private _chartApi: IChartApi | undefined;
  private _tzDisplay = new TZDisplay();
  private _options: IAdvanceChartOptions;

  public get tzDisplay() {
    return this._tzDisplay;
  }

  constructor(
    private container: HTMLElement,
    options?: Partial<IAdvanceChartOptions>
  ) {
    super();
    this._options = merge(cloneDeep(defaultAdvanceChartOptions), options ?? {});
    this._tzDisplay.applyOptions({
      tzDisplay: this._options.tzDisplay,
      locale: this._options.locale
    });
  }

  get chartApi(): IChartApi {
    if (!this._chartApi) {
      throw new Error(`Chart API is not initialized`);
    }
    return this._chartApi;
  }

  get numberFormatter() {
    return NumberFormatterFactory.formatter("en");
  }

  private _getChartOptions() {
    return {
      layout: {
        attributionLogo: false,
        panes: {
          separatorColor: "#e0e3eb",
          enableResize: false,
        },
        fontSize: this._options.fontSize,
        fontFamily: this._options.fontFamily,
        textColor: this._options.axesColor,
      },
      autoSize: true,
      height: this._options.height,
      localization: {
        locale: this._options.locale,
        percentageFormatter: (percentageValue: number) =>
          this.numberFormatter.percent(percentageValue / 100),
        timeFormatter: (time: Time) => this._tzDisplay.format(timeToDate(time)),
      },
      timeScale: {
        borderVisible: false,
        rightOffset: 10,
        maxBarSpacing: 40,
        minBarSpacing: 4,
        secondsVisible: true,
        timeVisible: true,
        tickMarkFormatter: (time: Time, tickMarkType: TickMarkType) =>
          this._tzDisplay.tickMarkFormatter(timeToDate(time), tickMarkType),
      },
      overlayPriceScales: {
        scaleMargins: {
          bottom: 0.05,
          top: 0.05,
        },
      },
      leftPriceScale: {
        borderVisible: false,
      },
      handleScale: {
        axisPressedMouseMove: false,
      },
      rightPriceScale: {
        borderVisible: false,
        mode: this._options.priceScaleMode,
      },

      grid: {
        horzLines: {
          visible: false,
        },
        vertLines: {
          color: this._options.gridColor,
        }
      },
    } satisfies DeepPartial<ChartOptions>;
  }

  get options() {
    return this._options;
  }

  get maxBar () {
    const maxBarSpacing = this.chartApi.options().timeScale.maxBarSpacing;
    const width = this.chartApi.timeScale().width();

    return Math.round(width / maxBarSpacing)
  }

  onInitialize() {
    const options = this._getChartOptions();
    this._chartApi = createChart(this.container, options);
  }

  applyOptions(options: Partial<IAdvanceChartOptions>) {
    const oldOptions = this._options;
    this._options = merge(cloneDeep(oldOptions), options);

    if(options.locale) {
      this.chartApi.applyOptions({localization: { locale: options.locale }})
      this._tzDisplay.applyOptions({locale: options.locale})
    }

    if(options.tzDisplay) {
      this._tzDisplay.applyOptions({tzDisplay: options.tzDisplay})
    }
    
    this.eventBus.fire<ChartCorePluginEvents>({
      type: 'chartOptionsChanged',
      payload: [options, oldOptions]
    })
  }

  fitRange(range: {from: Logical, to: Logical}) {
    const rightOffset = this.chartApi.options().timeScale.rightOffset
    const space = range.to - range.from
    const barVisiable = this.maxBar - rightOffset
    this.chartApi.timeScale().setVisibleLogicalRange({
      from: range.from, 
      to: (space < barVisiable ? range.from + barVisiable : range.to) + rightOffset
    });
  }

  destroy() {
    super.destroy();
    this._chartApi?.remove();
    this._chartApi = undefined;
  }
}
