import { ReactNode, useEffect, useState } from 'react';
import {useChartContext} from '../context';
import {ChartIndicator, ChartIndicatorOptions, DataManagerPluginEvents} from "@sharegraph-mini/advance-charts";
import {IIndicatorBar} from "@sharegraph-mini/advance-charts";

export function Legend<
  IOptions extends ChartIndicatorOptions = ChartIndicatorOptions,
  IIndicatorData extends readonly number[] = number[]
>({
  indicator,
  renderer,
  name
}: {
  indicator: ChartIndicator<IOptions, IIndicatorData>;
  renderer: (d: IIndicatorBar<IIndicatorData> | undefined) => ReactNode;
  name: string
}) {
  const [data, dataSet] = useState<IIndicatorBar<IIndicatorData> | undefined>();
  const [lastPoint, lastPointSet] = useState<IIndicatorBar<IIndicatorData> | undefined>();
  const { getChart } = useChartContext()

  useEffect(() => {
    indicator.dataHovered().subscribe(dataSet);
    return () => indicator.dataHovered().unsubscribe(dataSet);
  }, [indicator]);

  useEffect(() => {
    const advanceChart = getChart()
    if(!advanceChart) return;
    const handle = () => lastPointSet(indicator.lastPoint())
    handle()
    return advanceChart.eventBus.subscribe<DataManagerPluginEvents>({
      type: 'dataSetChanged',
      handler: handle
    })
    
  }, [getChart, indicator])
  
  const dataShow = data ?? lastPoint

  if(!dataShow) return null
  return <div className="chart-legend-item">{name} {renderer(data ?? lastPoint)}</div>;
}
