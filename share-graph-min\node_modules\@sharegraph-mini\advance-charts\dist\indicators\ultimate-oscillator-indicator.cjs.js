"use strict";var W=Object.defineProperty;var j=(n,s,e)=>s in n?W(n,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[s]=e;var k=(n,s,e)=>j(n,typeof s!="symbol"?s+"":s,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const H=require("lightweight-charts"),U=require("./abstract-indicator.cjs.js"),L=require("../custom-primitive/primitive/region.cjs.js"),z=require("../helpers/utils.cjs.js"),M={color:"#3179f5",priceLineColor:"#a1c3ff",backgroundColor:"#d2e0fa",period1:7,period2:14,period3:28,weight1:4,weight2:2,weight3:1,overlay:!1};class B extends U.ChartIndicator{constructor(e,o,r){super(e,o);k(this,"ultimateOscillatorSeries");this.ultimateOscillatorSeries=e.addSeries(H.LineSeries,{color:this.options.color,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"ultimateoscillator",autoscaleInfoProvider:z.autoScaleInfoProviderCreator({maxValue:100,minValue:0})},r),this.ultimateOscillatorSeries.attachPrimitive(new L.RegionPrimitive({upPrice:75,lowPrice:70,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor})),this.ultimateOscillatorSeries.attachPrimitive(new L.RegionPrimitive({upPrice:30,lowPrice:25,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return M}formula(e){const o=this.options.period1,r=this.options.period2,a=this.options.period3,d=this.options.weight1,g=this.options.weight2,m=this.options.weight3,S=e.symbol.high,v=e.symbol.low,b=e.symbol.close,h=Math.max(o,r,a),V=e.new_var(S,h+1),D=e.new_var(v,h+1),w=e.new_var(b,h+1);if(!V.calculable()||!D.calculable()||!w.calculable())return;const f=w.get(1),O=Math.min(v,f),q=Math.max(S,f),R=b-O,A=q-O,P=e.new_var(R,h),C=e.new_var(A,h);if(!P.calculable()||!C.calculable())return;const c=P.getAll(),u=C.getAll();let x=0,I=0,y=0;if(c.length>=o&&u.length>=o){const p=c.slice(-o).reduce((t,i)=>t+i,0),l=u.slice(-o).reduce((t,i)=>t+i,0);x=l!==0?p/l:0}else return;if(c.length>=r&&u.length>=r){const p=c.slice(-r).reduce((t,i)=>t+i,0),l=u.slice(-r).reduce((t,i)=>t+i,0);I=l!==0?p/l:0}else return;if(c.length>=a&&u.length>=a){const p=c.slice(-a).reduce((t,i)=>t+i,0),l=u.slice(-a).reduce((t,i)=>t+i,0);y=l!==0?p/l:0}else return;const T=d*x+g*I+m*y,_=d+g+m;return[_!==0?100*T/_:0]}applyIndicatorData(){const e=[];for(const o of this._executionContext.data){const r=o.value;if(!r)continue;const a=o.time;e.push({time:a,value:r[0]})}this.ultimateOscillatorSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.ultimateOscillatorSeries)}_applyOptions(){this.ultimateOscillatorSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.ultimateOscillatorSeries.moveToPane(e)}getPaneIndex(){return this.ultimateOscillatorSeries.getPane().paneIndex()}}exports.default=B;exports.defaultOptions=M;
//# sourceMappingURL=ultimate-oscillator-indicator.cjs.js.map
