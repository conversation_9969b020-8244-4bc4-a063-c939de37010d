import { ISeriesApi, Nominal, SeriesType, SingleValueData, Time, WhitespaceData} from "lightweight-charts";
import { SMAIndicatorOptions } from "./sma-indicator";
import { Context, IIndicatorBar } from "../helpers/execution-indicator";
import { SeriesPrimitiveBase } from "../custom-primitive/primitive-base";
import { LineData, LinePrimitivePaneView } from "../custom-primitive/pane-view/line";
import { ChartIndicator } from "./abstract-indicator";

export interface EMAIndicatorOptions extends SMAIndicatorOptions {}

export const defaultOptions: EMAIndicatorOptions = {
    color: "#d26400",
    period: 9,
    overlay: true
}

export class EMAPrimitive extends SeriesPrimitiveBase<
SingleValueData | WhitespaceData
> {
    linePrimitive: LinePrimitivePaneView;
    constructor(protected source: EMAIndicator) {
        super();
        this.linePrimitive = new LinePrimitivePaneView({
            lineColor: this.source.options.color,
        });
        this._paneViews = [this.linePrimitive];
    }

    update(indicatorBars: IIndicatorBar<EMAData>[]) {
        const lineData: LineData[] = []
        for(const bar of indicatorBars) {
            const value = bar.value
            if(value) lineData.push({time: bar.time as Time, price: value[0]})
        }

        this.linePrimitive.update(lineData);
    }
}

export type EMAData = readonly [Nominal<number, 'EMA'>]

export default class EMAIndicator extends ChartIndicator<EMAIndicatorOptions, EMAData> {
    emaPrimitive = new EMAPrimitive(this)

    getDefaultOptions(): EMAIndicatorOptions {
        return defaultOptions
    }

    _mainSeriesChanged(series: ISeriesApi<SeriesType>): void {
        series.attachPrimitive(this.emaPrimitive)
    }

    remove(): void {
        super.remove();
        this.mainSeries?.detachPrimitive(this.emaPrimitive)
    }

    applyIndicatorData(): void {
        this.emaPrimitive.update(
            this._executionContext.data
        )
    }

    formula(c: Context) {
        
        const period = this.options.period
        const close = c.symbol.close

        const alpha = 2 / (period + 1);

        const closeSeries = c.new_var(close, period);
        const emaSeries = c.new_var(NaN, 2);

        if(!closeSeries.calculable()) return;
        const previusEMA = emaSeries.get(1)

        let ema: number;

        if(isNaN(previusEMA)) {
            const closes = closeSeries.getAll();
            ema = closes.reduce((sum, val) => sum + val, 0) / period;
            emaSeries.set(ema);

            return [ema as Nominal<number, 'EMA'>] as EMAData
        } else {
            ema = alpha * close + (1 - alpha) * previusEMA;
        }

        emaSeries.set(ema);

        return [ema as Nominal<number, 'EMA'>] as EMAData
    }

}