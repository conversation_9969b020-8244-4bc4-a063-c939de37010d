import {Delegate} from "../../helpers";
import {IEventBus, IEventDefinition, IEventFire, IEventHandler, IEventSubscribe} from "./interface";

export class EventBus implements IEventBus {
  private _listeners = new Map<string, Delegate<unknown>>();

  public fire<T extends IEventDefinition>(params: IEventFire<T>): void {
    const {type, payload} = params as {type: string, payload: unknown};
    
    this._listeners.get(type as string)?.fire(payload);
  }

  public subscribe<T extends IEventDefinition>(params: IEventSubscribe<T>, linkedObject?: unknown, singleshot?: boolean): () => void {
    const {type, handler} = params as {type: string, handler: IEventHandler};

    if (!this._listeners.has(type as string)) {
      this._listeners.set(type as string, new Delegate());
    }
    
    this._listeners.get(type as string)!.subscribe(handler as <PERSON>Event<PERSON>and<PERSON>, linkedObject, singleshot);

    // Return unsubscribe function
    return () => this.unsubscribe(params);
  }

  public unsubscribe<T extends IEventDefinition>(params: IEventSubscribe<T>): void {
    const {type, handler} = params as {type: string, handler: IEventHandler};

    this._listeners.get(type as string)?.unsubscribe(handler as IEventHandler);
  }

  public removeAllListeners(event?: string): void {
    if (event) {
      this._listeners.delete(event);
    } else {
      this._listeners.clear();
    }
  }

  public destroy(): void {
    this.removeAllListeners();
  }
} 

export class EventBusWrapper implements Omit<IEventBus, 'removeAllListeners'> {
  private _cleanups: (() => void)[] = [];
  constructor(private eventBus: IEventBus) {}

  public eventFactory(factoryFn: () => () => void) {
    const cleanup = factoryFn();
    this._cleanups.push(cleanup);
    return cleanup;
  }

  public fire<T extends IEventDefinition>(params: IEventFire<T>): void {
    this.eventBus.fire(params);
  }

  public subscribe<T extends IEventDefinition>(params: IEventSubscribe<T>): () => void {
    const cleanup = this.eventBus.subscribe(params, this);
    this._cleanups.push(cleanup);
    return cleanup;
  }

  public unsubscribe<T extends IEventDefinition>(params: IEventSubscribe<T>): void {
    this.eventBus.unsubscribe(params);
  }
  
  public destroy(): void {
    this._cleanups.forEach(cleanup => cleanup());
    this._cleanups = [];
  }
}