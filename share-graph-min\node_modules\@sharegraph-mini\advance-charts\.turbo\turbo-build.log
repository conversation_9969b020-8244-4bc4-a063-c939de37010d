yarn run v1.22.22
warning package.json: No license field
$ tsc && vite build
[36mvite v6.3.5 [32mbuilding for production...[36m[39m
transforming...
[32m✓[39m 43 modules transformed.
rendering chunks...

[vite:dts] Start generate declaration files...
computing gzip size...
[2mdist/[22m[36madvance-chart/i-advance-chart.es.js             [39m[1m[2m 0.21 kB[22m[1m[22m[2m │ gzip: 0.18 kB[22m[2m │ map:  2.25 kB[22m
[2mdist/[22m[36mhelpers/assertions.es.js                        [39m[1m[2m 0.28 kB[22m[1m[22m[2m │ gzip: 0.18 kB[22m[2m │ map:  1.37 kB[22m
[2mdist/[22m[36mhelpers/line-style.es.js                        [39m[1m[2m 0.42 kB[22m[1m[22m[2m │ gzip: 0.24 kB[22m[2m │ map:  1.15 kB[22m
[2mdist/[22m[36mhelpers/mergeData.es.js                         [39m[1m[2m 0.49 kB[22m[1m[22m[2m │ gzip: 0.28 kB[22m[2m │ map:  2.60 kB[22m
[2mdist/[22m[36mhelpers/dayjs-setup.es.js                       [39m[1m[2m 0.55 kB[22m[1m[22m[2m │ gzip: 0.21 kB[22m[2m │ map:  0.68 kB[22m
[2mdist/[22m[36madvance-chart/time-key.es.js                    [39m[1m[2m 0.78 kB[22m[1m[22m[2m │ gzip: 0.44 kB[22m[2m │ map:  2.52 kB[22m
[2mdist/[22m[36mindicators/indicator-factory.es.js              [39m[1m[2m 0.85 kB[22m[1m[22m[2m │ gzip: 0.49 kB[22m[2m │ map:  2.55 kB[22m
[2mdist/[22m[36mhelpers/log.es.js                               [39m[1m[2m 1.06 kB[22m[1m[22m[2m │ gzip: 0.47 kB[22m[2m │ map:  3.21 kB[22m
[2mdist/[22m[36mhelpers/color.es.js                             [39m[1m[2m 1.07 kB[22m[1m[22m[2m │ gzip: 0.63 kB[22m[2m │ map:  3.38 kB[22m
[2mdist/[22m[36mhelpers/delegate.es.js                          [39m[1m[2m 1.07 kB[22m[1m[22m[2m │ gzip: 0.50 kB[22m[2m │ map:  3.37 kB[22m
[2mdist/[22m[36mcustom-primitive/pane-view/line.es.js           [39m[1m[2m 1.14 kB[22m[1m[22m[2m │ gzip: 0.58 kB[22m[2m │ map:  3.11 kB[22m
[2mdist/[22m[36mhelpers/utils.es.js                             [39m[1m[2m 1.40 kB[22m[1m[22m[2m │ gzip: 0.66 kB[22m[2m │ map:  4.41 kB[22m
[2mdist/[22m[36mcustom-primitive/pane-view/region.es.js         [39m[1m[2m 1.59 kB[22m[1m[22m[2m │ gzip: 0.70 kB[22m[2m │ map:  5.83 kB[22m
[2mdist/[22m[36mcustom-primitive/pane-view/band.es.js           [39m[1m[2m 1.62 kB[22m[1m[22m[2m │ gzip: 0.73 kB[22m[2m │ map:  4.50 kB[22m
[2mdist/[22m[36mindicators/index.es.js                          [39m[1m[2m 1.63 kB[22m[1m[22m[2m │ gzip: 0.47 kB[22m[2m │ map:  3.86 kB[22m
[2mdist/[22m[36mhelpers/number-formatter.es.js                  [39m[1m[2m 1.69 kB[22m[1m[22m[2m │ gzip: 0.62 kB[22m[2m │ map:  3.37 kB[22m
[2mdist/[22m[36mindicators/sma-indicator.es.js                  [39m[1m[2m 1.73 kB[22m[1m[22m[2m │ gzip: 0.80 kB[22m[2m │ map:  3.80 kB[22m
[2mdist/[22m[36mindicators/momentum-indicator.es.js             [39m[1m[2m 1.80 kB[22m[1m[22m[2m │ gzip: 0.84 kB[22m[2m │ map:  4.69 kB[22m
[2mdist/[22m[36mindicators/ema-indicator.es.js                  [39m[1m[2m 1.81 kB[22m[1m[22m[2m │ gzip: 0.87 kB[22m[2m │ map:  4.78 kB[22m
[2mdist/[22m[36mindicators/wma-indicator.es.js                  [39m[1m[2m 1.83 kB[22m[1m[22m[2m │ gzip: 0.85 kB[22m[2m │ map:  4.62 kB[22m
[2mdist/[22m[36mindicators/rsi-indicator.es.js                  [39m[1m[2m 2.15 kB[22m[1m[22m[2m │ gzip: 0.95 kB[22m[2m │ map:  4.68 kB[22m
[2mdist/[22m[36madvance-chart/DisplayTimezone.es.js             [39m[1m[2m 2.15 kB[22m[1m[22m[2m │ gzip: 0.79 kB[22m[2m │ map:  4.34 kB[22m
[2mdist/[22m[36madvance-chart/market.es.js                      [39m[1m[2m 2.16 kB[22m[1m[22m[2m │ gzip: 0.74 kB[22m[2m │ map:  6.36 kB[22m
[2mdist/[22m[36mindicators/vroc-indicator.es.js                 [39m[1m[2m 2.19 kB[22m[1m[22m[2m │ gzip: 0.98 kB[22m[2m │ map:  5.25 kB[22m
[2mdist/[22m[36mcustom-primitive/primitive/region.es.js         [39m[1m[2m 2.32 kB[22m[1m[22m[2m │ gzip: 0.91 kB[22m[2m │ map:  5.55 kB[22m
[2mdist/[22m[36mindicators/volume-indicator.es.js               [39m[1m[2m 2.34 kB[22m[1m[22m[2m │ gzip: 0.95 kB[22m[2m │ map:  5.65 kB[22m
[2mdist/[22m[36mindicators/williams-indicator.es.js             [39m[1m[2m 2.36 kB[22m[1m[22m[2m │ gzip: 1.03 kB[22m[2m │ map:  5.80 kB[22m
[2mdist/[22m[36mhelpers/execution-indicator.es.js               [39m[1m[2m 2.77 kB[22m[1m[22m[2m │ gzip: 1.02 kB[22m[2m │ map:  9.50 kB[22m
[2mdist/[22m[36mindicators/mass-index-indicator.es.js           [39m[1m[2m 2.78 kB[22m[1m[22m[2m │ gzip: 1.19 kB[22m[2m │ map:  8.08 kB[22m
[2mdist/[22m[36mindicators/abstract-indicator.es.js             [39m[1m[2m 3.03 kB[22m[1m[22m[2m │ gzip: 1.09 kB[22m[2m │ map:  7.42 kB[22m
[2mdist/[22m[36mindicators/stochastic-indicator.es.js           [39m[1m[2m 3.04 kB[22m[1m[22m[2m │ gzip: 1.19 kB[22m[2m │ map:  7.06 kB[22m
[2mdist/[22m[36mindicators/bb-indicator.es.js                   [39m[1m[2m 3.07 kB[22m[1m[22m[2m │ gzip: 1.06 kB[22m[2m │ map:  7.43 kB[22m
[2mdist/[22m[36mindicators/ultimate-oscillator-indicator.es.js  [39m[1m[2m 3.38 kB[22m[1m[22m[2m │ gzip: 1.30 kB[22m[2m │ map:  9.50 kB[22m
[2mdist/[22m[36mindicators/macd-indicator.es.js                 [39m[1m[2m 3.38 kB[22m[1m[22m[2m │ gzip: 1.16 kB[22m[2m │ map:  8.63 kB[22m
[2mdist/[22m[36mcustom-primitive/primitive-base.es.js           [39m[1m[2m 3.86 kB[22m[1m[22m[2m │ gzip: 1.32 kB[22m[2m │ map:  9.32 kB[22m
[2mdist/[22m[36madvance-charts.es.js                            [39m[1m[2m 4.55 kB[22m[1m[22m[2m │ gzip: 1.26 kB[22m[2m │ map:  0.14 kB[22m
[2mdist/[22m[36mindicators/dmi-indicator.es.js                  [39m[1m[2m 4.63 kB[22m[1m[22m[2m │ gzip: 1.61 kB[22m[2m │ map: 13.75 kB[22m
[2mdist/[22m[36madvance-chart/data-feed.es.js                   [39m[1m[2m 6.20 kB[22m[1m[22m[2m │ gzip: 1.99 kB[22m[2m │ map: 16.22 kB[22m
[2mdist/[22m[36madvance-chart/advance-chart.es.js               [39m[1m[2m14.29 kB[22m[1m[22m[2m │ gzip: 3.69 kB[22m[2m │ map: 31.46 kB[22m
[vite:dts] Declaration files built in 5326ms.

[2mdist/[22m[36madvance-chart/i-advance-chart.cjs.js             [39m[1m[2m 0.24 kB[22m[1m[22m[2m │ gzip: 0.21 kB[22m[2m │ map:  2.25 kB[22m
[2mdist/[22m[36mhelpers/assertions.cjs.js                        [39m[1m[2m 0.32 kB[22m[1m[22m[2m │ gzip: 0.22 kB[22m[2m │ map:  1.37 kB[22m
[2mdist/[22m[36mhelpers/dayjs-setup.cjs.js                       [39m[1m[2m 0.44 kB[22m[1m[22m[2m │ gzip: 0.26 kB[22m[2m │ map:  0.67 kB[22m
[2mdist/[22m[36mhelpers/mergeData.cjs.js                         [39m[1m[2m 0.45 kB[22m[1m[22m[2m │ gzip: 0.30 kB[22m[2m │ map:  2.58 kB[22m
[2mdist/[22m[36mhelpers/line-style.cjs.js                        [39m[1m[2m 0.46 kB[22m[1m[22m[2m │ gzip: 0.28 kB[22m[2m │ map:  1.15 kB[22m
[2mdist/[22m[36madvance-chart/time-key.cjs.js                    [39m[1m[2m 0.67 kB[22m[1m[22m[2m │ gzip: 0.44 kB[22m[2m │ map:  2.50 kB[22m
[2mdist/[22m[36mindicators/indicator-factory.cjs.js              [39m[1m[2m 0.76 kB[22m[1m[22m[2m │ gzip: 0.47 kB[22m[2m │ map:  2.52 kB[22m
[2mdist/[22m[36mhelpers/color.cjs.js                             [39m[1m[2m 0.80 kB[22m[1m[22m[2m │ gzip: 0.51 kB[22m[2m │ map:  3.29 kB[22m
[2mdist/[22m[36mhelpers/log.cjs.js                               [39m[1m[2m 0.88 kB[22m[1m[22m[2m │ gzip: 0.46 kB[22m[2m │ map:  3.06 kB[22m
[2mdist/[22m[36mhelpers/delegate.cjs.js                          [39m[1m[2m 0.92 kB[22m[1m[22m[2m │ gzip: 0.49 kB[22m[2m │ map:  3.25 kB[22m
[2mdist/[22m[36mcustom-primitive/pane-view/line.cjs.js           [39m[1m[2m 1.02 kB[22m[1m[22m[2m │ gzip: 0.57 kB[22m[2m │ map:  3.04 kB[22m
[2mdist/[22m[36mhelpers/utils.cjs.js                             [39m[1m[2m 1.20 kB[22m[1m[22m[2m │ gzip: 0.63 kB[22m[2m │ map:  4.36 kB[22m
[2mdist/[22m[36mcustom-primitive/pane-view/region.cjs.js         [39m[1m[2m 1.26 kB[22m[1m[22m[2m │ gzip: 0.66 kB[22m[2m │ map:  5.66 kB[22m
[2mdist/[22m[36mcustom-primitive/pane-view/band.cjs.js           [39m[1m[2m 1.34 kB[22m[1m[22m[2m │ gzip: 0.68 kB[22m[2m │ map:  4.37 kB[22m
[2mdist/[22m[36mhelpers/number-formatter.cjs.js                  [39m[1m[2m 1.40 kB[22m[1m[22m[2m │ gzip: 0.55 kB[22m[2m │ map:  3.23 kB[22m
[2mdist/[22m[36mindicators/sma-indicator.cjs.js                  [39m[1m[2m 1.48 kB[22m[1m[22m[2m │ gzip: 0.76 kB[22m[2m │ map:  3.65 kB[22m
[2mdist/[22m[36mindicators/momentum-indicator.cjs.js             [39m[1m[2m 1.52 kB[22m[1m[22m[2m │ gzip: 0.80 kB[22m[2m │ map:  4.54 kB[22m
[2mdist/[22m[36mindicators/ema-indicator.cjs.js                  [39m[1m[2m 1.53 kB[22m[1m[22m[2m │ gzip: 0.81 kB[22m[2m │ map:  4.64 kB[22m
[2mdist/[22m[36mindicators/wma-indicator.cjs.js                  [39m[1m[2m 1.56 kB[22m[1m[22m[2m │ gzip: 0.81 kB[22m[2m │ map:  4.48 kB[22m
[2mdist/[22m[36mindicators/vroc-indicator.cjs.js                 [39m[1m[2m 1.78 kB[22m[1m[22m[2m │ gzip: 0.90 kB[22m[2m │ map:  5.06 kB[22m
[2mdist/[22m[36mindicators/rsi-indicator.cjs.js                  [39m[1m[2m 1.79 kB[22m[1m[22m[2m │ gzip: 0.90 kB[22m[2m │ map:  4.48 kB[22m
[2mdist/[22m[36madvance-chart/market.cjs.js                      [39m[1m[2m 1.81 kB[22m[1m[22m[2m │ gzip: 0.70 kB[22m[2m │ map:  6.14 kB[22m
[2mdist/[22m[36madvance-chart/DisplayTimezone.cjs.js             [39m[1m[2m 1.83 kB[22m[1m[22m[2m │ gzip: 0.74 kB[22m[2m │ map:  4.19 kB[22m
[2mdist/[22m[36mindicators/volume-indicator.cjs.js               [39m[1m[2m 1.87 kB[22m[1m[22m[2m │ gzip: 0.87 kB[22m[2m │ map:  5.38 kB[22m
[2mdist/[22m[36mcustom-primitive/primitive/region.cjs.js         [39m[1m[2m 1.95 kB[22m[1m[22m[2m │ gzip: 0.84 kB[22m[2m │ map:  5.38 kB[22m
[2mdist/[22m[36mindicators/williams-indicator.cjs.js             [39m[1m[2m 1.98 kB[22m[1m[22m[2m │ gzip: 0.97 kB[22m[2m │ map:  5.61 kB[22m
[2mdist/[22m[36mindicators/index.cjs.js                          [39m[1m[2m 2.16 kB[22m[1m[22m[2m │ gzip: 0.53 kB[22m[2m │ map:  3.97 kB[22m
[2mdist/[22m[36mindicators/mass-index-indicator.cjs.js           [39m[1m[2m 2.18 kB[22m[1m[22m[2m │ gzip: 1.05 kB[22m[2m │ map:  7.87 kB[22m
[2mdist/[22m[36mhelpers/execution-indicator.cjs.js               [39m[1m[2m 2.22 kB[22m[1m[22m[2m │ gzip: 0.94 kB[22m[2m │ map:  9.18 kB[22m
[2mdist/[22m[36mindicators/abstract-indicator.cjs.js             [39m[1m[2m 2.49 kB[22m[1m[22m[2m │ gzip: 1.01 kB[22m[2m │ map:  7.14 kB[22m
[2mdist/[22m[36mindicators/bb-indicator.cjs.js                   [39m[1m[2m 2.51 kB[22m[1m[22m[2m │ gzip: 0.99 kB[22m[2m │ map:  7.03 kB[22m
[2mdist/[22m[36mindicators/ultimate-oscillator-indicator.cjs.js  [39m[1m[2m 2.54 kB[22m[1m[22m[2m │ gzip: 1.12 kB[22m[2m │ map:  9.21 kB[22m
[2mdist/[22m[36mindicators/stochastic-indicator.cjs.js           [39m[1m[2m 2.55 kB[22m[1m[22m[2m │ gzip: 1.11 kB[22m[2m │ map:  6.85 kB[22m
[2mdist/[22m[36mindicators/macd-indicator.cjs.js                 [39m[1m[2m 2.87 kB[22m[1m[22m[2m │ gzip: 1.09 kB[22m[2m │ map:  8.44 kB[22m
[2mdist/[22m[36mcustom-primitive/primitive-base.cjs.js           [39m[1m[2m 2.95 kB[22m[1m[22m[2m │ gzip: 1.10 kB[22m[2m │ map:  8.86 kB[22m
[2mdist/[22m[36mindicators/dmi-indicator.cjs.js                  [39m[1m[2m 3.66 kB[22m[1m[22m[2m │ gzip: 1.44 kB[22m[2m │ map: 13.42 kB[22m
[2mdist/[22m[36madvance-charts.cjs.js                            [39m[1m[2m 4.19 kB[22m[1m[22m[2m │ gzip: 1.09 kB[22m[2m │ map:  0.10 kB[22m
[2mdist/[22m[36madvance-chart/data-feed.cjs.js                   [39m[1m[2m 5.14 kB[22m[1m[22m[2m │ gzip: 1.80 kB[22m[2m │ map: 15.84 kB[22m
[2mdist/[22m[36madvance-chart/advance-chart.cjs.js               [39m[1m[2m10.80 kB[22m[1m[22m[2m │ gzip: 3.14 kB[22m[2m │ map: 29.97 kB[22m
[32m✓ built in 9.07s[39m
Done in 16.00s.
