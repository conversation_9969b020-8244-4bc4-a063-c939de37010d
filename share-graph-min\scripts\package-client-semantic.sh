#!/bin/sh
set -e

# Script to package client application after semantic-release
# Usage: ./package-client-semantic.sh <version>

# If no version specified, try to get from Git tag, otherwise use a default
if [ -z "$1" ]; then
  # Check if we're on a Git tag
  if [ -n "$CI_COMMIT_TAG" ]; then
    VERSION=${CI_COMMIT_TAG#v}
    # PACKAGE_NAME=${VERSION}
    echo "Using version from Git tag: $VERSION"
  # Check if we're on a release branch
  # elif [[ $(git symbolic-ref --short HEAD) =~ ^release/v ]]; then
  #   VERSION=$(git symbolic-ref --short HEAD | sed 's/^release\/v//')
  #   PACKAGE_NAME=${VERSION}
  #   echo "Using version from release branch: $VERSION"
  else
    # Use timestamp-based version for dev builds
    VERSION="0.0.0-${CI_COMMIT_BRANCH}_$(date -u +%Y%m%d%H%M)"
    # PACKAGE_NAME="${CI_COMMIT_BRANCH}"
    echo "Using development version: $VERSION"
  fi
else
  VERSION=$1
  #PACKAGE_NAME=$1
  echo "Using specified version: $VERSION"
fi

PACKAGE_NAME="${PACKAGE_SOURCE}.tar.gz"

if [ -z "$VERSION" ]; then
  echo "Error: Version is required"
  echo "Usage: $0 <version>"
  exit 1
fi

CLIENT_DIR="."
BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

echo "Packaging client application for version: $VERSION"

# Check if client build exists
if [ ! -d "${CLIENT_DIR}/dist" ]; then
  echo "Error: Client build directory not found. Run build first."
  exit 1
fi

cd ${CLIENT_DIR}

echo "Creating MSDeploy package $PACKAGE_SOURCE/ for client app version $VERSION"
mkdir -p $PACKAGE_SOURCE
cp -r dist/* $PACKAGE_SOURCE/

# Create version file with semantic-release info
cat > msdeploy_package/version.txt << EOF
Version: $VERSION
Commit: ${CI_COMMIT_SHA:-unknown}
Build Time: $BUILD_DATE
Branch: ${CI_COMMIT_REF_NAME:-unknown}
Generated by: semantic-release
EOF

# Copy version.js if it exists (created by update-versions-semantic.sh)
if [ -f "src/version.js" ]; then
  cp src/version.js msdeploy_package/static/js/ 2>/dev/null || true
fi

# Archive the package
tar -czf $PACKAGE_NAME $PACKAGE_SOURCE/
# cd msdeploy_package
# zip -r "../client_${VERSION}.zip" *
# cd ..

# Cleanup
rm -rf $PACKAGE_SOURCE

echo "✅ Client package created: ${PACKAGE_NAME}.tar.gz"
echo "📦 Package ready for deployment"
