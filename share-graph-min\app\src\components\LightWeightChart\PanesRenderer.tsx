import { FC, ReactNode, useEffect, useMemo, useState } from 'react';
import { createPortal } from 'react-dom';
import { AdvanceChart, ChartCorePluginEvents,  } from "@sharegraph-mini/advance-charts";
import { useChartContext } from './context';
import {IGroupIndicatorByPane} from "@sharegraph-mini/advance-charts";

const PanesRenderer: FC<{
  render: (pane: IGroupIndicatorByPane, advanceChart: AdvanceChart) => ReactNode;
}> = ({ render }) => {
  const { getChart } = useChartContext();
  const [panes, panesSet] = useState<
    { paneGroup: IGroupIndicatorByPane; container: HTMLElement }[]
  >([]);

  useEffect(() => {
    const chart = getChart()
    if (!chart) return;
    const advanceChartApi = chart.api();
    const handle = () => {
      const result = advanceChartApi
        .groupIndicatorByPane()
        .map((paneGroup) => ({
          paneGroup,
          container: paneGroup.pane.getHTMLElement().children[1] as HTMLElement,
        }));
      panesSet(result);
    };

    const retry = () => {
      try {
        handle();
      } catch {
        requestAnimationFrame(handle)
      }
    };
    retry();
    return chart.eventBus.subscribe<ChartCorePluginEvents>({
      type: 'chartOptionsChanged',
      handler: () => retry()
    })
  }, [getChart]);

  const chart = getChart()

  return (
    <>
      {useMemo(
        () =>
          chart ? panes.map(({ paneGroup, container }) =>
            createPortal(render(paneGroup, chart), container)
          ) : null,
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [panes]
      )}
    </>
  );
};

export default PanesRenderer;
