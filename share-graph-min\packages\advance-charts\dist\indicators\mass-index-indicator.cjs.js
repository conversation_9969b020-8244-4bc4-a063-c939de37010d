"use strict";var M=Object.defineProperty;var w=(r,t,e)=>t in r?M(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var S=(r,t,e)=>w(r,typeof t!="symbol"?t+"":t,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const C=require("lightweight-charts"),V=require("./abstract-indicator.cjs.js"),E=require("../custom-primitive/primitive/region.cjs.js"),_=require("../helpers/utils.cjs.js"),P={color:"#3179f5",priceLineColor:"#a1c3ff",backgroundColor:"#ff98001a",emaPeriod:9,sumPeriod:25,overlay:!1};class y extends V.ChartIndicator{constructor(e,s,o){super(e,s);S(this,"massIndexSeries");this.massIndexSeries=e.addSeries(C.LineSeries,{color:this.options.color,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"massindex",autoscaleInfoProvider:_.autoScaleInfoProviderCreator({maxValue:30,minValue:20})},o),this.massIndexSeries.attachPrimitive(new E.RegionPrimitive({upPrice:27.5,lowPrice:26.5,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return P}formula(e){const s=this.options.emaPeriod,o=this.options.sumPeriod,c=e.symbol.high,b=e.symbol.low,m=c-b,l=2/(s+1),p=e.new_var(m,s),h=e.new_var(NaN,2);if(!p.calculable())return;const f=h.get(1);let i;isNaN(f)?i=p.getAll().reduce((n,d)=>n+d,0)/s:i=l*m+(1-l)*f,h.set(i);const g=e.new_var(i,s),I=e.new_var(NaN,2);if(!g.calculable())return;const v=I.get(1);let a;isNaN(v)?a=g.getAll().reduce((n,d)=>n+d,0)/s:a=l*i+(1-l)*v,I.set(a);const A=a!==0?i/a:1,x=e.new_var(A,o);return x.calculable()?[x.getAll().reduce((u,n)=>u+n,0)]:void 0}applyIndicatorData(){const e=[];for(const s of this._executionContext.data){const o=s.value;if(!o)continue;const c=s.time;e.push({time:c,value:o[0]})}this.massIndexSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.massIndexSeries)}_applyOptions(){this.massIndexSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.massIndexSeries.moveToPane(e)}getPaneIndex(){return this.massIndexSeries.getPane().paneIndex()}}exports.default=y;exports.defaultOptions=P;
//# sourceMappingURL=mass-index-indicator.cjs.js.map
