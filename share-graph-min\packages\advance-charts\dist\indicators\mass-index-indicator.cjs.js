"use strict";var M=Object.defineProperty;var y=(r,t,e)=>t in r?M(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var v=(r,t,e)=>y(r,typeof t!="symbol"?t+"":t,e);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const _=require("lightweight-charts"),A=require("./abstract-indicator.cjs.js"),V=require("../custom-primitive/primitive/region.cjs.js"),D=require("../helpers/utils.cjs.js"),x={color:"rgba(255, 152, 0, 1)",priceLineColor:"rgba(150, 150, 150, 0.35)",backgroundColor:"#ff98001a",emaPeriod:9,sumPeriod:25,overlay:!1};class E extends A.ChartIndicator{constructor(e,o,s){super(e,o);v(this,"massIndexSeries");this.massIndexSeries=e.addSeries(_.LineSeries,{color:this.options.color,lineWidth:2,priceLineVisible:!1,crosshairMarkerVisible:!1,priceScaleId:"massindex",autoscaleInfoProvider:D.autoScaleInfoProviderCreator({maxValue:30,minValue:20})},s),this.massIndexSeries.attachPrimitive(new V.RegionPrimitive({upPrice:27.5,lowPrice:26.5,lineColor:this.options.priceLineColor,backgroundColor:this.options.backgroundColor}))}getDefaultOptions(){return x}formula(e){const o=this.options.emaPeriod,s=this.options.sumPeriod,n=e.symbol.high,c=e.symbol.low,d=o*2+s,S=e.new_var(n,d),P=e.new_var(c,d);if(!S.calculable()||!P.calculable())return;const u=n-c,l=2/(o+1),m=e.new_var(0,2),h=m.get(1);let i;isNaN(h)?i=u:i=l*u+(1-l)*h,m.set(i);const p=e.new_var(0,2),I=p.get(1);let a;isNaN(I)?a=i:a=l*i+(1-l)*I,p.set(a);const b=a!==0?i/a:1,f=e.new_var(b,s+1);if(!f.calculable())return;const g=f.getAll();return g.length<s?void 0:[g.slice(-s).reduce((w,C)=>w+C,0)]}applyIndicatorData(){const e=[];for(const o of this._executionContext.data){const s=o.value;if(!s)continue;const n=o.time;e.push({time:n,value:s[0]})}this.massIndexSeries.setData(e)}remove(){super.remove(),this.chart.removeSeries(this.massIndexSeries)}_applyOptions(){this.massIndexSeries.applyOptions({color:this.options.color}),this.applyIndicatorData()}setPaneIndex(e){this.massIndexSeries.moveToPane(e)}getPaneIndex(){return this.massIndexSeries.getPane().paneIndex()}}exports.default=E;exports.defaultOptions=x;
//# sourceMappingURL=mass-index-indicator.cjs.js.map
