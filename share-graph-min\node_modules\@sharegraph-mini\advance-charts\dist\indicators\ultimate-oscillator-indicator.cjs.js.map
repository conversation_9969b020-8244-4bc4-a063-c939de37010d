{"version": 3, "file": "ultimate-oscillator-indicator.cjs.js", "sources": ["../../src/indicators/ultimate-oscillator-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  period1: number,\n  period2: number,\n  period3: number,\n  weight1: number,\n  weight2: number,\n  weight3: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: UltimateOscillatorIndicatorOptions = {\n  color: \"#3179f5\",     // for Ultimate Oscillator\n  priceLineColor: \"#a1c3ff\",\n  backgroundColor: '#d2e0fa',\n  period1: 7,      // Short period as per TradingView standard\n  period2: 14,     // Medium period as per TradingView standard\n  period3: 28,     // Long period as per TradingView standard\n  weight1: 4,      // Weight for short period\n  weight2: 2,      // Weight for medium period\n  weight3: 1,      // Weight for long period\n  overlay: false\n}\n\nexport type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>\n\nexport type UltimateOscillatorData = [UltimateOscillatorLine]\n\nexport default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {\n  ultimateOscillatorSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.ultimateOscillatorSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'ultimateoscillator',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    // Add region primitives for visual reference at 30 and 70 thresholds\n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 75,\n        lowPrice: 70,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n    \n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 30,\n        lowPrice: 25,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): UltimateOscillatorIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): UltimateOscillatorData | undefined {\n    const period1 = this.options.period1;\n    const period2 = this.options.period2;\n    const period3 = this.options.period3;\n    const weight1 = this.options.weight1;\n    const weight2 = this.options.weight2;\n    const weight3 = this.options.weight3;\n\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n    const close = c.symbol.close;\n\n    // We need enough data for the longest period calculation\n    const maxPeriod = Math.max(period1, period2, period3);\n\n    // Get previous close for True Range and Buying Pressure calculations\n    const closeSeries = c.new_var(close, 2);\n    if (!closeSeries.calculable()) {\n      return;\n    }\n    const prevClose = closeSeries.get(1);\n\n    // Step 1: Calculate Buying Pressure (BP) and True Range (TR)\n    // BP = Close - Minimum(Low, Previous Close)\n    // TR = Maximum(High, Previous Close) - Minimum(Low, Previous Close)\n\n    const minLowPrevClose = Math.min(low, prevClose);\n    const maxHighPrevClose = Math.max(high, prevClose);\n\n    const buyingPressure = close - minLowPrevClose;\n    const trueRange = maxHighPrevClose - minLowPrevClose;\n\n    // Step 2: Maintain history for BP and TR calculations\n    // Store BP and TR values for period calculations\n    const bpSeries = c.new_var(buyingPressure, maxPeriod);\n    const trSeries = c.new_var(trueRange, maxPeriod);\n\n    if (!bpSeries.calculable() || !trSeries.calculable()) {\n      return;\n    }\n\n    const bpValues = bpSeries.getAll();\n    const trValues = trSeries.getAll();\n\n    // Ensure we have enough data for the longest period\n    if (bpValues.length < maxPeriod || trValues.length < maxPeriod) {\n      return;\n    }\n\n    // Step 3: Calculate averages for each period\n    // Average7 = (7-period BP Sum) / (7-period TR Sum)\n    // Average14 = (14-period BP Sum) / (14-period TR Sum)\n    // Average28 = (28-period BP Sum) / (28-period TR Sum)\n\n    const bp1Sum = bpValues.slice(-period1).reduce((sum, val) => sum + val, 0);\n    const tr1Sum = trValues.slice(-period1).reduce((sum, val) => sum + val, 0);\n    const average1 = tr1Sum !== 0 ? bp1Sum / tr1Sum : 0;\n\n    const bp2Sum = bpValues.slice(-period2).reduce((sum, val) => sum + val, 0);\n    const tr2Sum = trValues.slice(-period2).reduce((sum, val) => sum + val, 0);\n    const average2 = tr2Sum !== 0 ? bp2Sum / tr2Sum : 0;\n\n    const bp3Sum = bpValues.slice(-period3).reduce((sum, val) => sum + val, 0);\n    const tr3Sum = trValues.slice(-period3).reduce((sum, val) => sum + val, 0);\n    const average3 = tr3Sum !== 0 ? bp3Sum / tr3Sum : 0;\n\n    // Step 4: Calculate Ultimate Oscillator using exact TradingView/StockCharts formula\n    // UO = 100 × [(4×Average7) + (2×Average14) + Average28] / (4+2+1)\n    const weightedSum = (weight1 * average1) + (weight2 * average2) + (weight3 * average3);\n    const totalWeight = weight1 + weight2 + weight3;\n    const ultimateOscillator = totalWeight !== 0 ? (100 * weightedSum) / totalWeight : 0;\n\n    return [ultimateOscillator as UltimateOscillatorLine];\n  }\n\n  applyIndicatorData() {\n    const ultimateOscillatorData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      ultimateOscillatorData.push({time, value: value[0]});\n    }\n\n    this.ultimateOscillatorSeries.setData(ultimateOscillatorData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.ultimateOscillatorSeries);\n  }\n\n  _applyOptions() {\n    this.ultimateOscillatorSeries.applyOptions({color: this.options.color});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.ultimateOscillatorSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.ultimateOscillatorSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "UltimateOscillatorIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period1", "period2", "period3", "weight1", "weight2", "weight3", "high", "low", "close", "max<PERSON><PERSON><PERSON>", "closeSeries", "prevClose", "minLowPrevClose", "maxHighPrevClose", "buyingPressure", "trueRang<PERSON>", "bpSeries", "trSeries", "bpV<PERSON>ues", "tr<PERSON><PERSON><PERSON>", "bp1Sum", "sum", "val", "tr1Sum", "average1", "bp2Sum", "tr2Sum", "average2", "bp3Sum", "tr3Sum", "average3", "weightedSum", "totalWeight", "ultimateOscillatorData", "bar", "value", "time"], "mappings": "6bAkBaA,EAAqD,CAChE,MAAO,UACP,eAAgB,UAChB,gBAAiB,UACjB,QAAS,EACT,QAAS,GACT,QAAS,GACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACX,EAMA,MAAqBC,UAAoCC,EAAAA,cAA2E,CAGlI,YAAYC,EAAkBC,EAAuDC,EAAoB,CACvG,MAAMF,EAAOC,CAAO,EAHtBE,EAAA,iCAKO,KAAA,yBAA2BH,EAAM,UAAUI,EAAAA,WAAY,CAC1D,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,qBACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,IAAK,SAAU,CAAE,CAAA,GAC/EH,CAAS,EAGZ,KAAK,yBAAyB,gBAC5B,IAAII,kBAAgB,CAClB,QAAS,GACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,EAEA,KAAK,yBAAyB,gBAC5B,IAAIA,kBAAgB,CAClB,QAAS,GACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAAwD,CAC/C,OAAAT,CAAA,CAGT,QAAQU,EAAgD,CAChD,MAAAC,EAAU,KAAK,QAAQ,QACvBC,EAAU,KAAK,QAAQ,QACvBC,EAAU,KAAK,QAAQ,QACvBC,EAAU,KAAK,QAAQ,QACvBC,EAAU,KAAK,QAAQ,QACvBC,EAAU,KAAK,QAAQ,QAEvBC,EAAOP,EAAE,OAAO,KAChBQ,EAAMR,EAAE,OAAO,IACfS,EAAQT,EAAE,OAAO,MAGjBU,EAAY,KAAK,IAAIT,EAASC,EAASC,CAAO,EAG9CQ,EAAcX,EAAE,QAAQS,EAAO,CAAC,EAClC,GAAA,CAACE,EAAY,aACf,OAEI,MAAAC,EAAYD,EAAY,IAAI,CAAC,EAM7BE,EAAkB,KAAK,IAAIL,EAAKI,CAAS,EACzCE,EAAmB,KAAK,IAAIP,EAAMK,CAAS,EAE3CG,EAAiBN,EAAQI,EACzBG,EAAYF,EAAmBD,EAI/BI,EAAWjB,EAAE,QAAQe,EAAgBL,CAAS,EAC9CQ,EAAWlB,EAAE,QAAQgB,EAAWN,CAAS,EAE/C,GAAI,CAACO,EAAS,WAAA,GAAgB,CAACC,EAAS,aACtC,OAGI,MAAAC,EAAWF,EAAS,OAAO,EAC3BG,EAAWF,EAAS,OAAO,EAGjC,GAAIC,EAAS,OAAST,GAAaU,EAAS,OAASV,EACnD,OAQF,MAAMW,EAASF,EAAS,MAAM,CAAClB,CAAO,EAAE,OAAO,CAACqB,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EACnEC,EAASJ,EAAS,MAAM,CAACnB,CAAO,EAAE,OAAO,CAACqB,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EACnEE,EAAWD,IAAW,EAAIH,EAASG,EAAS,EAE5CE,EAASP,EAAS,MAAM,CAACjB,CAAO,EAAE,OAAO,CAACoB,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EACnEI,EAASP,EAAS,MAAM,CAAClB,CAAO,EAAE,OAAO,CAACoB,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EACnEK,EAAWD,IAAW,EAAID,EAASC,EAAS,EAE5CE,EAASV,EAAS,MAAM,CAAChB,CAAO,EAAE,OAAO,CAACmB,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EACnEO,EAASV,EAAS,MAAM,CAACjB,CAAO,EAAE,OAAO,CAACmB,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EACnEQ,EAAWD,IAAW,EAAID,EAASC,EAAS,EAI5CE,EAAe5B,EAAUqB,EAAapB,EAAUuB,EAAatB,EAAUyB,EACvEE,EAAc7B,EAAUC,EAAUC,EAGxC,MAAO,CAFoB2B,IAAgB,EAAK,IAAMD,EAAeC,EAAc,CAE/B,CAAA,CAGtD,oBAAqB,CACnB,MAAMC,EAA4C,CAAC,EAEzC,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MAClB,GAAG,CAACC,EAAO,SAEX,MAAMC,EAAOF,EAAI,KACjBD,EAAuB,KAAK,CAAC,KAAAG,EAAM,MAAOD,EAAM,CAAC,EAAE,CAAA,CAGhD,KAAA,yBAAyB,QAAQF,CAAsB,CAAA,CAG9D,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,wBAAwB,CAAA,CAGvD,eAAgB,CACd,KAAK,yBAAyB,aAAa,CAAC,MAAO,KAAK,QAAQ,MAAM,EACtE,KAAK,mBAAmB,CAAA,CAG1B,aAAavC,EAAmB,CACzB,KAAA,yBAAyB,WAAWA,CAAS,CAAA,CAGpD,cAAuB,CACrB,OAAO,KAAK,yBAAyB,QAAQ,EAAE,UAAU,CAAA,CAE7D"}