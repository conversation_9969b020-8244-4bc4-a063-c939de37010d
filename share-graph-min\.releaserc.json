{"tagFormat": "v${version}", "branches": ["master", {"name": "release/+([0-9])?(.{+([0-9]),x}).x", "prerelease": "rc"}], "plugins": [["@semantic-release/commit-analyzer", {"releaseRules": [{"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "perf", "release": "patch"}, {"type": "revert", "release": "patch"}, {"type": "docs", "release": false}, {"type": "style", "release": false}, {"type": "chore", "release": false}, {"type": "refactor", "release": "patch"}, {"type": "test", "release": false}, {"type": "build", "release": false}, {"type": "ci", "release": false}, {"breaking": true, "release": "major"}]}], "@semantic-release/release-notes-generator", ["@semantic-release/git", {"assets": ["version.json", "package.json"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}]]}