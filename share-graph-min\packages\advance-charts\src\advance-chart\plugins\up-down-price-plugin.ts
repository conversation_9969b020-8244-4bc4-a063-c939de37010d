import {BasePlugin} from "../core/base-plugin";
import {ChartTypePlugin} from "./chart-type-plugin";
import {ChartCorePlugin, ChartCorePluginEvents} from "./chart-core-plugin";
import {DataManagerPlugin, DataManagerPluginEvents} from "./data-manager-plugin";

export class UpDownPricePlugin extends BasePlugin {
  static name = 'up-down-price-plugin';

  private get chartCore() {
    return this.context.core.getPlugin<ChartCorePlugin>(ChartCorePlugin.name);
  }

  private get dataManager() {
    return this.context.core.getPlugin<DataManagerPlugin>(DataManagerPlugin.name);
  }

  private get chartType() {
    return this.context.core.getPlugin<ChartTypePlugin>(ChartTypePlugin.name);
  }

  protected onInitialize(): void {
    this.eventBus.subscribe<DataManagerPluginEvents>({
      type: 'dataSetChanged',
      handler: () => {
        this.tryDrawUpDownLine();
      }
    })

    this.eventBus.subscribe<ChartCorePluginEvents>({
      type: 'chartOptionsChanged',
      handler: () => {
        this.tryDrawUpDownLine();
      }
    })

    this.eventBus.eventFactory(() => {
      const handler = this.tryDrawUpDownLine.bind(this);
      const timeScale = this.chartCore.chartApi.timeScale()
      timeScale.subscribeVisibleTimeRangeChange(handler);

      return () => timeScale.unsubscribeVisibleTimeRangeChange(handler)
    })
  }

  private tryDrawUpDownLine() {
    const mainSeries = this.chartType.mainSeries;
    const options = this.chartCore.options;
    if (!mainSeries) return;
    if (!options.highLowLineVisible) {
      mainSeries.priceLines().forEach(line => mainSeries?.removePriceLine(line))
      return;
    }
    const dataSet = this.dataManager.dataSegment;
    const low = dataSet.reduce(
      (acc, item) => Math.min(acc, item.close),
      Number.MAX_SAFE_INTEGER
    );
    const high = dataSet.reduce(
      (acc, item) => Math.max(acc, item.close),
      Number.MIN_SAFE_INTEGER
    );
    const [lowPriceLine, highPriceLine] = mainSeries?.priceLines() ?? [];

    if (lowPriceLine) {
      lowPriceLine.applyOptions({
        price: low,
        color: options.downColor,
      });
    } else {
      mainSeries.createPriceLine({
        price: low,
        color: options.downColor,
      });
    }
    if (highPriceLine) {
      highPriceLine.applyOptions({
        price: high,
        color: options.upColor,
      });
    } else {
      mainSeries.createPriceLine({
        price: high,
        color: options.upColor,
      });
    }
  }
}