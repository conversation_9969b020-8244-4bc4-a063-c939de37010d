import {Logical, LogicalRange, Time} from "lightweight-charts";
import {BasePlugin} from "../core/base-plugin";
import {ChartCorePlugin} from "./chart-core-plugin";
import {DataManagerPlugin} from "./data-manager-plugin";
import {dayjs, dayjsToTime, Interval, OHLCVSimple, Period, timeToDayjs} from "../../advance-charts";
import {IEventDefinition} from "../core/interface";
import {Dayjs} from "dayjs";

export interface IDataFetchQuery {
  from: Date;
  to: Date;
  interval: Interval;
}

export type IDataFetchUtils = {
  forward: (time: Time, step: number) => Dayjs
}

export interface IDataFetch {
  refeshTime?: number;

  /**
   * Fetches the initial data when:
   * - The chart is first loaded
   * - The user changes the visible time range
   * - The interval/period is changed
   * 
   * This provides the base dataset that the chart will display initially.
   * Subsequent updates/pagination will build upon this data.
   */
  fetchInitialData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;

  /**
   * Fetches historical data that's outside the current visible chart range.
   * This data is used to:
   * - Calculate indicators that require lookback periods
   * - Provide seamless scrolling experience when user reaches chart boundaries
   * Automatically triggered when scrolling to the beginning/end of loaded data.
   */
  fetchPaginationData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;

  /**
   * Fetches updated data periodically based on refreshTime interval.
   * Queries data from the timestamp of last data point to current time.
   * New data points with same timestamps will overwrite existing ones.
   * Used for real-time updates to the chart.
   */
  fetchUpdateData(param: IDataFetchQuery, utils: IDataFetchUtils): Promise<OHLCVSimple[]>;
}

export type DataFeedPluginEvents = IEventDefinition<{
  dataFeedLoading: boolean
}>

export class DataFeedPlugin extends BasePlugin {
  static name = 'data-feed-plugin';

  private _endOfData = false;
  private _loading = false;
  private _initialData = false;

  get loading() {
    return this._loading;
  }

  set loading(value: boolean) {
    this._loading = value;
    this.eventBus.fire<DataFeedPluginEvents>({
      type: 'dataFeedLoading',
      payload: value
    })
  }

  constructor(protected dataFetch: IDataFetch) {
    super();
  }

  private get chartCore() {
    return this.context.core.getPlugin<ChartCorePlugin>(ChartCorePlugin.name);
  }

  private get chartApi() {
    return this.chartCore.chartApi;
  }

  private get dataManager() {
    return this.context.core.getPlugin<DataManagerPlugin>(DataManagerPlugin.name);
  }

  protected onInitialize(): void {
    this.eventBus.eventFactory(() => {
      const handler = this.onVisibleLogicalRangeChange.bind(this);
      const timeScale = this.chartCore.chartApi.timeScale()
      timeScale.subscribeVisibleLogicalRangeChange(handler);

      return () => timeScale.unsubscribeVisibleLogicalRangeChange(handler)
    });
  }

  private async onVisibleLogicalRangeChange (logicalRange: LogicalRange | null) {
    if(!logicalRange) return;
    const isSameRange = (range1: LogicalRange, range2: LogicalRange) => range1.from === range2.from && range1.to === range2.to
    if(this.loading) return
    this.loading = true
    try {
      while(this.isNeedPaging(logicalRange)) {
        await this.pagingData(logicalRange)
        const newRange = this.chartApi.timeScale().getVisibleLogicalRange();
        if(!newRange) break;
        if(isSameRange(newRange, logicalRange)) break;
        logicalRange = newRange
      }
    } finally {
      this.loading = false
    }
  }

  isNeedPaging(logicalRange: LogicalRange) {
    if (!this._initialData) return false;
    if (this.dataManager.masterData.length === 0) return false;
    if (this._endOfData) return false;
    const { from } = logicalRange;
    if (from > 30) return false;
    return true
  }

  async pagingData(logicalRange: LogicalRange) {
    const { from } = logicalRange;
    const toTime = this.dataManager.masterData[0].time;
    const toDate = timeToDayjs(toTime);
    const fromDate = this.forward(toTime, from - 200);
    
    const data = await this.dataFetch.fetchPaginationData({
      interval: this.dataManager.interval,
      from: fromDate.toDate(),
      to: toDate.toDate(),
    }, { forward: this.forward.bind(this) });

    if(this._destroyed) return
    
    if(!this.dataManager.updateMasterData(data)) {
      this._endOfData = true;
    }
  }

  forward(time: Time, step: number) {
    step = Math.round(step);
    const period = this.dataManager.interval.period
    switch (period) {
      case Period.minute:
        return timeToDayjs(time).add(step, 'minute');
      case Period.hour:
        return timeToDayjs(time).add(step, 'hour');
      case Period.day:
        return timeToDayjs(time).add(step, 'day');
      case Period.week:
        return timeToDayjs(time).add(step, 'week');
      case Period.month:
        return timeToDayjs(time).add(step, 'month');
      default:
        throw new Error(`Period : ${period} not support`)
    }
  } 

  async setRange({ from, to, interval }: IDataFetchQuery) {
    this.resetState();
    
    this.loading = true;
    try {
      const data = await this.dataFetch.fetchInitialData({ from, to, interval }, { forward: this.forward.bind(this) });
      this._initialData = true;
      this.dataManager.setMasterData(data, interval);
    } finally {
      this.loading = false;
    }
    
    const timeScale = this.chartApi.timeScale();
    const fromIndex = timeScale.timeToIndex(dayjsToTime(dayjs(from)), true)
    const toIndex = timeScale.timeToIndex(dayjsToTime(dayjs(to)), true)

    if(fromIndex !== undefined && toIndex !== undefined) {
      this.chartCore.fitRange({ from: fromIndex as unknown as Logical, to: toIndex as unknown as Logical })
    }
    
    await this.onVisibleLogicalRangeChange(
      timeScale.getVisibleLogicalRange()
    );
  }

  private resetState() {
    this.loading = false;
    this._initialData = false;
    this._endOfData = false;
  }
}