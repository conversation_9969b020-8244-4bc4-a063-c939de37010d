import {
  LineSeries,
  AreaSeries,
  BarSeries,
  BaselineSeries,
  CandlestickSeries,
  LastPriceAnimationMode,
  ISeriesApi,
  SeriesType,
  DataChangedScope,
} from "lightweight-charts";
import { BasePlugin } from "../core/base-plugin";
import { IAdvanceChartType } from "../i-advance-chart";
import { ChartCorePlugin, ChartCorePluginEvents } from "./chart-core-plugin";
import { IEventDefinition } from "../core/interface";
import {DataManagerPlugin, DataManagerPluginEvents} from "./data-manager-plugin";

export type ChartTypePluginEvents = IEventDefinition<{
  chartTypeChanged: IAdvanceChartType
}>

export class ChartTypePlugin extends BasePlugin {
  static name = "chart-type-plugin";

  private _mainSeries: ISeriesApi<SeriesType> | null = null;
  private _chartType: IAdvanceChartType | null = null;

  get mainSeries() {
    return this._mainSeries;
  }

  get chartType() {
    if (!this._chartType) {
      throw new Error('Chart type is not initialized');
    }
    return this._chartType;
  }

  get chartCore() {
    return this.context.core.getPlugin<ChartCorePlugin>(ChartCorePlugin.name);
  }

  protected onInitialize(): void {
    this.eventBus.subscribe<DataManagerPluginEvents>({
      type: 'dataSetChanged',
      handler: this.applyData.bind(this)
    });

    this.eventBus.subscribe<ChartCorePluginEvents>({
      type: 'chartOptionsChanged',
      handler: ([options]) => {
        if(options.priceLineVisible !== undefined) this._mainSeries?.applyOptions({
          priceLineVisible: options.priceLineVisible,
        });
      }
    });
  }

  private applyData(dataScope: DataChangedScope = 'full') {
    const dataManager = this.context.core.getPlugin<DataManagerPlugin>(DataManagerPlugin.name);
    const data = dataManager.dataSet;
    if(!data) return;

    if(dataScope === 'update') return this._mainSeries?.update(data[data.length - 1]);
    
    this._mainSeries?.setData(data.map(item => ({...item, value: item.close, customValues: {...item, value: item.close}})));
  }

  setChartType(type: IAdvanceChartType) {
    if (this._chartType === type) return; 
    const chartApi = this.chartCore.chartApi;
    const options = this.chartCore.options;
    let mainSeries: ISeriesApi<SeriesType>;

    switch (type) {
      case 'line':
        mainSeries = chartApi.addSeries(
          LineSeries,
          { 
            color: options.mainColor, 
            priceLineVisible: options.priceLineVisible,
            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,
            lineWidth: 2
          },
          0
        );
        break;
      case 'candle':
        mainSeries = chartApi.addSeries(
          CandlestickSeries,
          { upColor: options.upColor, downColor: options.downColor, priceLineVisible: options.priceLineVisible, },
          0
        );
        break;
      case 'mountain':
        mainSeries = chartApi.addSeries(
          AreaSeries,
          {
            topColor: options.mainColor,
            lineColor: options.mainColor,
            bottomColor: '#ffffff00',
            priceLineVisible: options.priceLineVisible,
            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,
            lineWidth: 2
          },
          0
        );
        break;
      case 'bar':
        mainSeries = chartApi.addSeries(
          BarSeries,
          { upColor: options.upColor, downColor: options.downColor, priceLineVisible: options.priceLineVisible },
          0
        );
        break;
      case 'baseline':
        mainSeries = chartApi.addSeries(
          BaselineSeries,
          {
            topLineColor: options.upColor,
            bottomLineColor: options.downColor,
            bottomFillColor1: 'transparent',
            bottomFillColor2: 'transparent',
            topFillColor1: 'transparent',
            topFillColor2: 'transparent',
            priceLineVisible: options.priceLineVisible,
            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,
            lineWidth: 2
          },
          0
        );
        break;
      case 'base-mountain':
        mainSeries = chartApi.addSeries(
          BaselineSeries,
          {
            topLineColor: options.upColor,
            bottomLineColor: options.downColor,
            priceLineVisible: options.priceLineVisible,
            lastPriceAnimation: LastPriceAnimationMode.OnDataUpdate,
            lineWidth: 2
          },
          0
        );
        break;
      default:
        throw new Error('Invalid chart type');
    }

    if(this._mainSeries) {
      this.chartCore.chartApi.removeSeries(this._mainSeries);
    }

    this._chartType = type;
    this._mainSeries = mainSeries;
    this.applyData();

    this.eventBus.fire<ChartTypePluginEvents>({
      type: 'chartTypeChanged',
      payload: type
    })  
  }
}
