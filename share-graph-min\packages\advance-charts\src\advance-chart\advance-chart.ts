import {
  DeepPartial,
} from 'lightweight-charts';

import { Delegate } from '../helpers/delegate';
import {IAdvanceChartOptions} from './i-advance-chart';
import {PluginManager} from './core/plugin-manager';
import {ChartCorePlugin} from './plugins/chart-core-plugin';
import {ChartTypePlugin} from './plugins/chart-type-plugin';
import {DataManagerPlugin} from './plugins/data-manager-plugin';
import {DataFeedPlugin, IDataFetch} from './plugins/data-feed-plugin';
import {IndicatorManagerPlugin} from './plugins/indicator-manager-plugin';
import {UpDownPricePlugin} from './plugins/up-down-price-plugin';
import {IPlugin} from './core/interface';
import {EventBusWrapper} from './core/event-bus';


export class AdvanceChart {
  private _corePlugin = new PluginManager()
  private _chartCorePlugin: ChartCorePlugin;
  private _chartTypePlugin: ChartTypePlugin;
  private _dataManagerPlugin: DataManagerPlugin;
  private _dataFeedPlugin: DataFeedPlugin;
  private _indicatorManagerPlugin: IndicatorManagerPlugin;

  // Create delegates for events
  private _destroyed = new Delegate<void>();
  private _optionChanged = new Delegate<void>();
  private _mainSeriesChanged = new Delegate<void>();
  
  private _eventBus: EventBusWrapper;

  constructor(
    container: HTMLElement, 
    dataFetch: IDataFetch,
    options?: DeepPartial<IAdvanceChartOptions>,
  ) {
    this._chartCorePlugin = this._corePlugin.registerPlugin(ChartCorePlugin.name, new ChartCorePlugin(container, options))
    this._chartTypePlugin = this._corePlugin.registerPlugin(ChartTypePlugin.name, new ChartTypePlugin())
    this._dataManagerPlugin = this._corePlugin.registerPlugin(DataManagerPlugin.name, new DataManagerPlugin())
    this._dataFeedPlugin = this._corePlugin.registerPlugin(DataFeedPlugin.name, new DataFeedPlugin(dataFetch))
    this._indicatorManagerPlugin = this._corePlugin.registerPlugin(IndicatorManagerPlugin.name, new IndicatorManagerPlugin())
    this._corePlugin.registerPlugin(UpDownPricePlugin.name, new UpDownPricePlugin());

    this._eventBus = new EventBusWrapper(this._corePlugin.eventBus);
  }

  get eventBus() {
    return this._eventBus;
  }

  api() {
    // Create a type that represents all methods available through the proxy
    type AllPluginsType = {
      [K in keyof ChartCorePlugin]: ChartCorePlugin[K]
    } & {
      [K in keyof ChartTypePlugin]: ChartTypePlugin[K]  
    } & {
      [K in keyof DataManagerPlugin]: DataManagerPlugin[K]
    } & {
      [K in keyof DataFeedPlugin]: DataFeedPlugin[K]
    } & {
      [K in keyof IndicatorManagerPlugin]: IndicatorManagerPlugin[K]
    };
    const all = [
      this._chartCorePlugin,
      this._chartTypePlugin,
      this._dataManagerPlugin,
      this._dataFeedPlugin,
      this._indicatorManagerPlugin
    ]
    return new Proxy({}, {
      get(_, prop) {
        const target = all.find(item => prop in item);
        if (!target) return undefined;
        const value = target[prop as keyof typeof target];
        if(typeof value === 'function') {
          return value.bind(target);
        }
        return value;
      }
    }) as AllPluginsType;
  }
  

  applyOptions(options: DeepPartial<IAdvanceChartOptions>) {
    this._chartCorePlugin.applyOptions(options);
    this._optionChanged.fire();
  }

  remove() {
    this._corePlugin.destroy();
    this._destroyed.fire();
  }

  registerPlugin<T extends IPlugin>(name: string, plugin: T): T {
    return this._corePlugin.registerPlugin(name, plugin);
  }

  unregisterPlugin(name: string): void {
    this._corePlugin.unregisterPlugin(name);
  }

  getPlugin<T extends IPlugin>(name: string): T {
    return this._corePlugin.getPlugin<T>(name);
  } 

  destroy() {
    this._corePlugin.destroy();
    this._destroyed.fire();
    this._optionChanged.destroy();
    this._mainSeriesChanged.destroy();
    this._eventBus.destroy();
  }
}
