import { useEffect, useState } from 'react';
import { useChartContext } from './context';
import { DataFeedPluginEvents, DataManagerPlugin, DataManagerPluginEvents } from '@sharegraph-mini/advance-charts';

const NoData = () => {
  const {getChart } = useChartContext();
  const [noData, noDataSet] = useState(false);
  const [loading, loadingSet] = useState(false);
  const show = !loading && noData
  useEffect(() => {
    const chart = getChart()
    if(!chart) return;
    const dataManager = chart.getPlugin<DataManagerPlugin>(DataManagerPlugin.name)

    return chart.eventBus.subscribe<DataManagerPluginEvents>({
      type: 'dataSetChanged',
      handler: () => {
        if (dataManager.masterData.length === 0) {
          noDataSet(true);
        } else {
          noDataSet(false);
        }
      }
    })
  }, [getChart]);

  useEffect(() => {
    const chart = getChart()
    if(!chart) return;
    
    return chart.eventBus.subscribe<DataFeedPluginEvents>({  
      type: 'dataFeedLoading',
      handler: (loading) => loadingSet(loading)
    })
  }, [getChart])
  return (
    <div
      style={{
        display: show ? 'flex' : 'none',
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: '2',
        flexDirection: 'column',
        alignItems: 'center',
      }}
    >
      <svg
        width={64}
        height={41}
        viewBox="0 0 64 41"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>No data</title>
        <g transform="translate(0 1)" fill="none" fillRule="evenodd">
          <ellipse fill="#f5f5f5" cx={32} cy={33} rx={32} ry={7} />
          <g fillRule="nonzero" stroke="#d9d9d9">
            <path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z" />
            <path
              d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
              fill="#fafafa"
            />
          </g>
        </g>
      </svg>
      <div style={{color: 'rgba(0, 0, 0, 0.50)'}}>No data</div>
    </div>
  );
};

export default NoData;
