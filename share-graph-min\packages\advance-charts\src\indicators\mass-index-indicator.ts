import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {RegionPrimitive} from "../custom-primitive/primitive/region";
import {autoScaleInfoProviderCreator} from "../helpers/utils";
import {Context} from "../helpers/execution-indicator";

export interface MassIndexIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  emaPeriod: number,
  sumPeriod: number,
  priceLineColor: string,
  backgroundColor: string
}

export const defaultOptions: MassIndexIndicatorOptions = {
  color: "#3179f5",     // for Mass Index
  priceLineColor: "#a1c3ff",
  backgroundColor: '#d2e0fa',
  emaPeriod: 9,      // 9-period EMA as per TradingView standard
  sumPeriod: 25,     // 25-period sum as per TradingView standard
  overlay: false
}

export type MassIndexLine = Nominal<number, 'MassIndex'>

export type MassIndexData = [MassIndexLine]

export default class MassIndexIndicator extends ChartIndicator<MassIndexIndicatorOptions, MassIndexData> {
  massIndexSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<MassIndexIndicatorOptions>, paneIndex?: number) {
    super(chart, options)
    
    this.massIndexSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'massindex',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 30, minValue: 20})
    }, paneIndex);
    
    // Add region primitive for visual reference at 27 threshold
    this.massIndexSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 27.5,
        lowPrice: 26.5,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): MassIndexIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): MassIndexData | undefined {
    const emaPeriod = this.options.emaPeriod;
    const sumPeriod = this.options.sumPeriod;
    const high = c.symbol.high;
    const low = c.symbol.low;

    // Calculate High-Low range
    const range = high - low;

    // Use custom EMA formula with alpha = 2/(n+1) as established in codebase
    const emaAlpha = 2 / (emaPeriod + 1);

    // Step 1: Calculate first EMA of (High-Low) range
    // Follow the same pattern as EMA indicator
    const rangeSeries = c.new_var(range, emaPeriod);
    const firstEMAVar = c.new_var(NaN, 2);

    if (!rangeSeries.calculable()) return;

    const prevFirstEMA = firstEMAVar.get(1);
    let currentFirstEMA: number;

    if (isNaN(prevFirstEMA)) {
      // Initialize first EMA with simple average of range values
      const ranges = rangeSeries.getAll();
      currentFirstEMA = ranges.reduce((sum, val) => sum + val, 0) / emaPeriod;
    } else {
      // EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]
      currentFirstEMA = emaAlpha * range + (1 - emaAlpha) * prevFirstEMA;
    }

    firstEMAVar.set(currentFirstEMA);

    // Step 2: Calculate second EMA of the first EMA
    // Follow the same pattern as EMA indicator
    const firstEMASeries = c.new_var(currentFirstEMA, emaPeriod);
    const secondEMAVar = c.new_var(NaN, 2);

    if (!firstEMASeries.calculable()) return;

    const prevSecondEMA = secondEMAVar.get(1);
    let currentSecondEMA: number;

    if (isNaN(prevSecondEMA)) {
      // Initialize second EMA with simple average of first EMA values
      const firstEMAValues = firstEMASeries.getAll();
      currentSecondEMA = firstEMAValues.reduce((sum, val) => sum + val, 0) / emaPeriod;
    } else {
      // EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]
      currentSecondEMA = emaAlpha * currentFirstEMA + (1 - emaAlpha) * prevSecondEMA;
    }

    secondEMAVar.set(currentSecondEMA);

    // Step 3: Calculate the ratio (First EMA / Second EMA)
    // Avoid division by zero and ensure reasonable ratio values
    const ratio = currentSecondEMA !== 0 ? currentFirstEMA / currentSecondEMA : 1;

    // Step 4: Calculate Mass Index as sum of ratios over sumPeriod
    const ratioSeries = c.new_var(ratio, sumPeriod);

    if (!ratioSeries.calculable()) return;

    const ratioValues = ratioSeries.getAll();

    // Sum all ratios to get Mass Index
    const massIndex = ratioValues.reduce((sum, val) => sum + val, 0);

    return [massIndex as MassIndexLine];
  }

  applyIndicatorData() {
    const massIndexData: SingleValueData[] = [];
    
    for(const bar of this._executionContext.data) {
      const value = bar.value;
      if(!value) continue;
      
      const time = bar.time as Time;
      massIndexData.push({time, value: value[0]});
    }

    this.massIndexSeries.setData(massIndexData);
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.massIndexSeries);
  }

  _applyOptions() {
    this.massIndexSeries.applyOptions({color: this.options.color});
    this.applyIndicatorData();
  }

  setPaneIndex(paneIndex: number) {
    this.massIndexSeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.massIndexSeries.getPane().paneIndex();
  }
}
