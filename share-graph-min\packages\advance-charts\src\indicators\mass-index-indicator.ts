import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {RegionPrimitive} from "../custom-primitive/primitive/region";
import {autoScaleInfoProviderCreator} from "../helpers/utils";
import {Context} from "../helpers/execution-indicator";

export interface MassIndexIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  emaPeriod: number,
  sumPeriod: number,
  priceLineColor: string,
  backgroundColor: string
}

export const defaultOptions: MassIndexIndicatorOptions = {
  color: "rgba(255, 152, 0, 1)",     // Orange for Mass Index
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: '#ff98001a',
  emaPeriod: 9,      // 9-period EMA as per TradingView standard
  sumPeriod: 25,     // 25-period sum as per TradingView standard
  overlay: false
}

export type MassIndexLine = Nominal<number, 'MassIndex'>

export type MassIndexData = [MassIndexLine]

export default class MassIndexIndicator extends ChartIndicator<MassIndexIndicatorOptions, MassIndexData> {
  massIndexSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<MassIndexIndicatorOptions>, paneIndex?: number) {
    super(chart, options)
    
    this.massIndexSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'massindex',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 30, minValue: 20})
    }, paneIndex);
    
    // Add region primitive for visual reference at 27 threshold
    this.massIndexSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 27.5,
        lowPrice: 26.5,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): MassIndexIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): MassIndexData | undefined {
    const emaPeriod = this.options.emaPeriod;
    const sumPeriod = this.options.sumPeriod;
    const high = c.symbol.high;
    const low = c.symbol.low;

    // We need enough data for EMA calculations and sum period
    // Need at least emaPeriod for first EMA, then emaPeriod more for second EMA, then sumPeriod for final sum
    const minDataNeeded = emaPeriod * 2 + sumPeriod;

    const highSeries = c.new_var(high, minDataNeeded);
    const lowSeries = c.new_var(low, minDataNeeded);

    if (!highSeries.calculable() || !lowSeries.calculable()) {
      return;
    }

    // Step 1: Calculate High-Low range
    const range = high - low;

    // Use custom EMA formula with alpha = 2/(n+1) as established in codebase
    const emaAlpha = 2 / (emaPeriod + 1);

    // Step 2: Calculate first 9-period EMA of (High-Low)
    const firstEMAVar = c.new_var(0, 2);
    const prevFirstEMA = firstEMAVar.get(1);

    let currentFirstEMA: number;

    if (isNaN(prevFirstEMA)) {
      // Initialize first EMA with current range value
      currentFirstEMA = range;
    } else {
      // EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]
      currentFirstEMA = emaAlpha * range + (1 - emaAlpha) * prevFirstEMA;
    }

    firstEMAVar.set(currentFirstEMA);

    // Step 3: Calculate second 9-period EMA of the first EMA
    const secondEMAVar = c.new_var(0, 2);
    const prevSecondEMA = secondEMAVar.get(1);

    let currentSecondEMA: number;

    if (isNaN(prevSecondEMA)) {
      // Initialize second EMA with current first EMA value
      currentSecondEMA = currentFirstEMA;
    } else {
      // EMA formula: EMA = alpha * x + (1 - alpha) * EMA[1]
      currentSecondEMA = emaAlpha * currentFirstEMA + (1 - emaAlpha) * prevSecondEMA;
    }

    secondEMAVar.set(currentSecondEMA);

    // Step 4: Calculate the ratio (First EMA / Second EMA)
    const ratio = currentSecondEMA !== 0 ? currentFirstEMA / currentSecondEMA : 1;

    // Step 5: Calculate Mass Index as sum of ratios over sumPeriod
    const ratioHistory = c.new_var(ratio, sumPeriod + 1);

    if (!ratioHistory.calculable()) {
      return;
    }

    const ratioValues = ratioHistory.getAll();
    if (ratioValues.length < sumPeriod) {
      return;
    }

    // Sum the last sumPeriod ratios to get Mass Index
    const massIndex = ratioValues.slice(-sumPeriod).reduce((sum, val) => sum + val, 0);

    return [massIndex as MassIndexLine];
  }

  applyIndicatorData() {
    const massIndexData: SingleValueData[] = [];
    
    for(const bar of this._executionContext.data) {
      const value = bar.value;
      if(!value) continue;
      
      const time = bar.time as Time;
      massIndexData.push({time, value: value[0]});
    }

    this.massIndexSeries.setData(massIndexData);
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.massIndexSeries);
  }

  _applyOptions() {
    this.massIndexSeries.applyOptions({color: this.options.color});
    this.applyIndicatorData();
  }

  setPaneIndex(paneIndex: number) {
    this.massIndexSeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.massIndexSeries.getPane().paneIndex();
  }
}
