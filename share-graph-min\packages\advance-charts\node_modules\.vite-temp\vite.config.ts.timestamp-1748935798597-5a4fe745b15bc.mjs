// vite.config.ts
import { defineConfig as viteDefineConfig } from "file:///C:/Users/<USER>/OneDrive%20-%20Eurolandcom%20AB/Desktop/shareGraph/share-graph-min/node_modules/vite/dist/node/index.js";

// package.json
var package_default = {
  name: "@sharegraph-mini/advance-charts",
  version: "0.0.0",
  type: "module",
  exports: {
    ".": {
      require: "./dist/advance-charts.cjs.js",
      import: "./dist/advance-charts.es.js"
    }
  },
  module: "./dist/advance-charts.es.js",
  types: "./dist/advance-charts.d.ts",
  scripts: {
    dev: "vite --port 4000",
    build: "tsc && vite build",
    preview: "vite preview",
    test: "vitest --run",
    "test:watch": "vitest",
    lint: "eslint ."
  },
  dependencies: {
    "lightweight-charts": "^5.0.5",
    "es-toolkit": "^1.30.1",
    dayjs: "^1.11.13",
    "fancy-canvas": "^2.1.0",
    technicalindicators: "^3.1.0"
  },
  devDependencies: {
    typescript: "~5.8.3",
    vite: "^6.3.5",
    "vite-plugin-dts": "^3.8.1",
    vitest: "^2.1.8",
    eslint: "^9.15.0",
    globals: "^15.12.0",
    "@eslint/js": "^9.15.0",
    "typescript-eslint": "^8.15.0"
  }
};

// vite.config.ts
import dts from "file:///C:/Users/<USER>/OneDrive%20-%20Eurolandcom%20AB/Desktop/shareGraph/share-graph-min/packages/advance-charts/node_modules/vite-plugin-dts/dist/index.mjs";
import { defineConfig, mergeConfig } from "file:///C:/Users/<USER>/OneDrive%20-%20Eurolandcom%20AB/Desktop/shareGraph/share-graph-min/node_modules/vitest/dist/config.js";
var vite_config_default = mergeConfig(viteDefineConfig({
  plugins: [
    dts({
      entryRoot: "src",
      afterDiagnostic: function(diagnostics) {
        if (diagnostics.length > 0)
          throw new Error("Please fix all TypeScript errors before building");
      }
    })
  ],
  build: {
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true
    },
    lib: {
      // Could also be a dictionary or array of multiple entry points.
      entry: "src/advance-charts.ts",
      name: package_default.name,
      fileName: "advance-charts",
      // Change this to the formats you want to support.
      // Don't forget to update your package.json as well.
      formats: ["es", "cjs"]
    },
    rollupOptions: {
      // External packages that should not be bundled into your library.
      external: Array.from(/* @__PURE__ */ new Set([
        "es-toolkit/compat",
        "dayjs/plugin/utc",
        "dayjs/plugin/timezone",
        "dayjs/plugin/weekYear",
        "dayjs/plugin/weekOfYear",
        ...Object.keys(package_default.dependencies ?? {})
      ])),
      output: {
        preserveModules: true,
        preserveModulesRoot: "src",
        exports: "named",
        entryFileNames: "[name].[format].js"
      }
    },
    sourcemap: true
  }
}), defineConfig({
  test: {
    environment: "jsdom",
    globals: true,
    setupFiles: "./src/setupTests.ts"
  }
}));
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
