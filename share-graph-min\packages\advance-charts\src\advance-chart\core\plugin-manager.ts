import { EventBus } from "./event-bus";
import { IEventDefinition, IPlugin, IPluginManager } from "./interface";

export type PluginManagerEvents = IEventDefinition<{
  PluginRegistered: { name: string; plugin: IPlugin };
  PluginUnregistered: { name: string; plugin: IPlugin };
  CoreDestroying: undefined;
}>;

export class PluginManager implements IPluginManager {
  private _plugins = new Map<string, IPlugin>();
  private _eventBus = new EventBus();
  private _destroyed = false;

  public get eventBus(): EventBus {
    return this._eventBus;
  }

  public get destroyed(): boolean {
    return this._destroyed;
  }

  public registerPlugin<T extends IPlugin>(name: string, plugin: T): T {
    if (this._plugins.has(name)) {
      throw new Error(`Plugin "${name}" is already registered`);
    }

    this._plugins.set(name, plugin);

    plugin.initialize({
      eventBus: this._eventBus,
      core: this,
    });

    this._eventBus.fire<PluginManagerEvents>({
      type: "PluginRegistered",
      payload: { name, plugin },
    });

    return plugin;
  }

  public unregisterPlugin(name: string): void {
    const plugin = this._plugins.get(name);
    if (plugin) {
      plugin.destroy();
      this._plugins.delete(name);
      this._eventBus.fire<PluginManagerEvents>({
        type: "PluginUnregistered",
        payload: { name, plugin },
      });
    }
  }

  public getPlugin<T extends IPlugin>(name: string): T {
    return this._plugins.get(name) as T;
  }

  public hasPlugin(name: string): boolean {
    return this._plugins.has(name);
  }

  public listPlugins(): string[] {
    return Array.from(this._plugins.keys());
  }

  public destroy(): void {
    if (this._destroyed) return;

    this._destroyed = true;
    this._eventBus.fire<PluginManagerEvents>({
      type: "CoreDestroying"
    });

    // Destroy all plugins
    for (const [name, plugin] of this._plugins) {
      try {
        plugin.destroy();
      } catch (error) {
        console.error(`Error destroying plugin "${name}":`, error);
      }
    }

    this._plugins.clear();
    this._eventBus.destroy();
  }
}
