{"version": 3, "file": "ultimate-oscillator-indicator.cjs.js", "sources": ["../../src/indicators/ultimate-oscillator-indicator.ts"], "sourcesContent": ["import { IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time } from \"lightweight-charts\";\nimport { ChartIndicator, ChartIndicatorOptions } from \"./abstract-indicator\";\nimport { RegionPrimitive } from \"../custom-primitive/primitive/region\";\nimport { autoScaleInfoProviderCreator } from \"../helpers/utils\";\nimport { Context } from \"../helpers/execution-indicator\";\n\nexport interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  period1: number,\n  period2: number,\n  period3: number,\n  weight1: number,\n  weight2: number,\n  weight3: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: UltimateOscillatorIndicatorOptions = {\n  color: \"#3179f5\",     // for Ultimate Oscillator\n  priceLineColor: \"#a1c3ff\",\n  backgroundColor: '#d2e0fa',\n  period1: 7,      // Short period as per TradingView standard\n  period2: 14,     // Medium period as per TradingView standard\n  period3: 28,     // Long period as per TradingView standard\n  weight1: 4,      // Weight for short period\n  weight2: 2,      // Weight for medium period\n  weight3: 1,      // Weight for long period\n  overlay: false\n}\n\nexport type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>\n\nexport type UltimateOscillatorData = [UltimateOscillatorLine]\n\nexport default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {\n  ultimateOscillatorSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n\n    this.ultimateOscillatorSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'ultimateoscillator',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({ maxValue: 100, minValue: 0 })\n    }, paneIndex);\n\n    // Add region primitives for visual reference at 30 and 70 thresholds\n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 75,\n        lowPrice: 70,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n\n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 30,\n        lowPrice: 25,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): UltimateOscillatorIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): UltimateOscillatorData | undefined {\n    const period1 = this.options.period1;\n    const period2 = this.options.period2;\n    const period3 = this.options.period3;\n    const weight1 = this.options.weight1;\n    const weight2 = this.options.weight2;\n    const weight3 = this.options.weight3;\n\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n    const close = c.symbol.close;\n\n    // Get MAX period needed first for proper series initialization\n    const maxPeriod = Math.max(period1, period2, period3);\n\n    // Create series to track previous close - need at least maxPeriod + 1 for lookback\n    const closeSeries = c.new_var(close, maxPeriod + 1);\n    if (!closeSeries.calculable()) return;\n\n    // Get previous close (handle first bar case)\n    const prevClose = closeSeries.get(1);\n\n    // BP = Close - Min(Low, PrevClose)\n    const minLowPrevClose = Math.min(low, prevClose);\n    const buyingPressure = close - minLowPrevClose;\n        \n    // TR = Max(High, PrevClose) - Min(Low, PrevClose)  \n    const maxHighPrevClose = Math.max(high, prevClose);\n    const trueRange = maxHighPrevClose - minLowPrevClose;\n\n    // Create series for BP and TR with proper period length\n    const bpSeries = c.new_var(buyingPressure, maxPeriod);\n    const trSeries = c.new_var(trueRange, maxPeriod);\n\n    if (!bpSeries.calculable() || !trSeries.calculable()) return;\n\n    // Check if we have enough data for the longest period\n    if (!bpSeries.calculable() || !trSeries.calculable()) return;\n\n    // Calculate period averages: Sum(BP) / Sum(TR) for each period\n    const getPeriodAverage = (period: number) => {\n        let bpSum = 0;\n        let trSum = 0;\n        \n        // Sum the most recent 'period' values\n        for (let i = 0; i < period; i++) {\n            bpSum += bpSeries.get(i);\n            trSum += trSeries.get(i);\n        }\n        \n        return trSum > 0 ? bpSum / trSum : 0;\n    };\n\n    const avg1 = getPeriodAverage(period1);  // 7-period average\n    const avg2 = getPeriodAverage(period2);  // 14-period average\n    const avg3 = getPeriodAverage(period3);  // 28-period average\n\n    // UO = 100 × [(4×Avg7) + (2×Avg14) + Avg28] / (4+2+1)\n    const weightedSum = (weight1 * avg1) + (weight2 * avg2) + (weight3 * avg3);\n    const totalWeight = weight1 + weight2 + weight3;\n    \n    let ultimateOscillator = totalWeight > 0 ? 100 * (weightedSum / totalWeight) : 0;\n\n    // Optional: Clamp to 0-100 range (TradingView doesn't explicitly clamp, but values should naturally stay in range)\n    // Remove this if you want exact TradingView behavior\n    // ultimateOscillator = Math.max(0, Math.min(100, ultimateOscillator));\n\n    return [ultimateOscillator as UltimateOscillatorLine];\n}\n\n  applyIndicatorData() {\n    const ultimateOscillatorData: SingleValueData[] = [];\n\n    for (const bar of this._executionContext.data) {\n      const value = bar.value;\n      if (!value) continue;\n\n      const time = bar.time as Time;\n      ultimateOscillatorData.push({ time, value: value[0] });\n    }\n\n    this.ultimateOscillatorSeries.setData(ultimateOscillatorData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.ultimateOscillatorSeries);\n  }\n\n  _applyOptions() {\n    this.ultimateOscillatorSeries.applyOptions({ color: this.options.color });\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.ultimateOscillatorSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.ultimateOscillatorSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "UltimateOscillatorIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period1", "period2", "period3", "weight1", "weight2", "weight3", "high", "low", "close", "max<PERSON><PERSON><PERSON>", "closeSeries", "prevClose", "minLowPrevClose", "buyingPressure", "trueRang<PERSON>", "bpSeries", "trSeries", "getPeriodAverage", "period", "bpSum", "trSum", "i", "avg1", "avg2", "avg3", "weightedSum", "totalWeight", "ultimateOscillatorData", "bar", "value", "time"], "mappings": "6bAkBaA,EAAqD,CAChE,MAAO,UACP,eAAgB,UAChB,gBAAiB,UACjB,QAAS,EACT,QAAS,GACT,QAAS,GACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACX,EAMA,MAAqBC,UAAoCC,EAAAA,cAA2E,CAGlI,YAAYC,EAAkBC,EAAuDC,EAAoB,CACvG,MAAMF,EAAOC,CAAO,EAHtBE,EAAA,iCAKO,KAAA,yBAA2BH,EAAM,UAAUI,EAAAA,WAAY,CAC1D,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,qBACd,sBAAuBC,EAA6B,6BAAA,CAAE,SAAU,IAAK,SAAU,CAAG,CAAA,GACjFH,CAAS,EAGZ,KAAK,yBAAyB,gBAC5B,IAAII,kBAAgB,CAClB,QAAS,GACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,EAEA,KAAK,yBAAyB,gBAC5B,IAAIA,kBAAgB,CAClB,QAAS,GACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAAwD,CAC/C,OAAAT,CAAA,CAGT,QAAQU,EAAgD,CAChD,MAAAC,EAAU,KAAK,QAAQ,QACvBC,EAAU,KAAK,QAAQ,QACvBC,EAAU,KAAK,QAAQ,QACvBC,EAAU,KAAK,QAAQ,QACvBC,EAAU,KAAK,QAAQ,QACvBC,EAAU,KAAK,QAAQ,QAEvBC,EAAOP,EAAE,OAAO,KAChBQ,EAAMR,EAAE,OAAO,IACfS,EAAQT,EAAE,OAAO,MAGjBU,EAAY,KAAK,IAAIT,EAASC,EAASC,CAAO,EAG9CQ,EAAcX,EAAE,QAAQS,EAAOC,EAAY,CAAC,EAC9C,GAAA,CAACC,EAAY,aAAc,OAGzB,MAAAC,EAAYD,EAAY,IAAI,CAAC,EAG7BE,EAAkB,KAAK,IAAIL,EAAKI,CAAS,EACzCE,EAAiBL,EAAQI,EAIzBE,EADmB,KAAK,IAAIR,EAAMK,CAAS,EACZC,EAG/BG,EAAWhB,EAAE,QAAQc,EAAgBJ,CAAS,EAC9CO,EAAWjB,EAAE,QAAQe,EAAWL,CAAS,EAK/C,GAHI,CAACM,EAAS,WAAA,GAAgB,CAACC,EAAS,cAGpC,CAACD,EAAS,WAAA,GAAgB,CAACC,EAAS,aAAc,OAGhD,MAAAC,EAAoBC,GAAmB,CACzC,IAAIC,EAAQ,EACRC,EAAQ,EAGZ,QAASC,EAAI,EAAGA,EAAIH,EAAQG,IACfF,GAAAJ,EAAS,IAAIM,CAAC,EACdD,GAAAJ,EAAS,IAAIK,CAAC,EAGpB,OAAAD,EAAQ,EAAID,EAAQC,EAAQ,CACvC,EAEME,EAAOL,EAAiBjB,CAAO,EAC/BuB,EAAON,EAAiBhB,CAAO,EAC/BuB,EAAOP,EAAiBf,CAAO,EAG/BuB,EAAetB,EAAUmB,EAASlB,EAAUmB,EAASlB,EAAUmB,EAC/DE,EAAcvB,EAAUC,EAAUC,EAQxC,MAAO,CANkBqB,EAAc,EAAI,KAAOD,EAAcC,GAAe,CAM3B,CAAA,CAGtD,oBAAqB,CACnB,MAAMC,EAA4C,CAAC,EAExC,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC7C,MAAMC,EAAQD,EAAI,MAClB,GAAI,CAACC,EAAO,SAEZ,MAAMC,EAAOF,EAAI,KACjBD,EAAuB,KAAK,CAAE,KAAAG,EAAM,MAAOD,EAAM,CAAC,EAAG,CAAA,CAGlD,KAAA,yBAAyB,QAAQF,CAAsB,CAAA,CAG9D,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,wBAAwB,CAAA,CAGvD,eAAgB,CACd,KAAK,yBAAyB,aAAa,CAAE,MAAO,KAAK,QAAQ,MAAO,EACxE,KAAK,mBAAmB,CAAA,CAG1B,aAAajC,EAAmB,CACzB,KAAA,yBAAyB,WAAWA,CAAS,CAAA,CAGpD,cAAuB,CACrB,OAAO,KAAK,yBAAyB,QAAQ,EAAE,UAAU,CAAA,CAE7D"}