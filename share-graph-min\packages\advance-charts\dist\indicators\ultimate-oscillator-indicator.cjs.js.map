{"version": 3, "file": "ultimate-oscillator-indicator.cjs.js", "sources": ["../../src/indicators/ultimate-oscillator-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  period1: number,\n  period2: number,\n  period3: number,\n  weight1: number,\n  weight2: number,\n  weight3: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: UltimateOscillatorIndicatorOptions = {\n  color: \"#3179f5\",     // for Ultimate Oscillator\n  priceLineColor: \"#a1c3ff\",\n  backgroundColor: '#d2e0fa',\n  period1: 7,      // Short period as per TradingView standard\n  period2: 14,     // Medium period as per TradingView standard\n  period3: 28,     // Long period as per TradingView standard\n  weight1: 4,      // Weight for short period\n  weight2: 2,      // Weight for medium period\n  weight3: 1,      // Weight for long period\n  overlay: false\n}\n\nexport type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>\n\nexport type UltimateOscillatorData = [UltimateOscillatorLine]\n\nexport default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {\n  ultimateOscillatorSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.ultimateOscillatorSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'ultimateoscillator',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    // Add region primitives for visual reference at 30 and 70 thresholds\n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 75,\n        lowPrice: 70,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n    \n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 30,\n        lowPrice: 25,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): UltimateOscillatorIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): UltimateOscillatorData | undefined {\n    const { period1, period2, period3, weight1, weight2, weight3 } = this.options;\n  \n    const high = c.symbol.high;\n    const low = c.symbol.low;\n    const close = c.symbol.close;\n  \n    const maxPeriod = Math.max(period1, period2, period3);\n  \n    // Lấy dữ liệu giá cần thiết\n    const highSeries = c.new_var(high, maxPeriod + 1);\n    const lowSeries = c.new_var(low, maxPeriod + 1);\n    const closeSeries = c.new_var(close, maxPeriod + 1);\n  \n    if (!highSeries.calculable() || !lowSeries.calculable() || !closeSeries.calculable()) {\n      return;\n    }\n  \n    const bpSeries: number[] = [];\n    const trSeries: number[] = [];\n  \n    for (let i = 1; i <= maxPeriod; i++) {\n      const currentHigh = highSeries.get(i);\n      const currentLow = lowSeries.get(i);\n      const currentClose = closeSeries.get(i);\n      const prevClose = closeSeries.get(i - 1);\n  \n      const minLowPrevClose = Math.min(currentLow, prevClose);\n      const maxHighPrevClose = Math.max(currentHigh, prevClose);\n  \n      const bp = currentClose - minLowPrevClose;\n      const tr = maxHighPrevClose - minLowPrevClose;\n  \n      bpSeries.push(bp);\n      trSeries.push(tr);\n    }\n  \n    const sum = (arr: number[], period: number) =>\n      arr.slice(-period).reduce((acc, val) => acc + val, 0);\n  \n    const avg1 = sum(bpSeries, period1) / sum(trSeries, period1);\n    const avg2 = sum(bpSeries, period2) / sum(trSeries, period2);\n    const avg3 = sum(bpSeries, period3) / sum(trSeries, period3);\n  \n    const totalWeight = weight1 + weight2 + weight3;\n    const uo = 100 * ((weight1 * avg1 + weight2 * avg2 + weight3 * avg3) / totalWeight);\n  \n    return [uo as UltimateOscillatorLine];\n  }\n  \n\n  applyIndicatorData() {\n    const ultimateOscillatorData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      ultimateOscillatorData.push({time, value: value[0]});\n    }\n\n    this.ultimateOscillatorSeries.setData(ultimateOscillatorData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.ultimateOscillatorSeries);\n  }\n\n  _applyOptions() {\n    this.ultimateOscillatorSeries.applyOptions({color: this.options.color});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.ultimateOscillatorSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.ultimateOscillatorSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "UltimateOscillatorIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period1", "period2", "period3", "weight1", "weight2", "weight3", "high", "low", "close", "max<PERSON><PERSON><PERSON>", "highSeries", "lowSeries", "closeSeries", "bpSeries", "trSeries", "i", "currentHigh", "currentLow", "currentClose", "prevClose", "minLowPrevClose", "maxHighPrevClose", "bp", "tr", "sum", "arr", "period", "acc", "val", "avg1", "avg2", "avg3", "totalWeight", "ultimateOscillatorData", "bar", "value", "time"], "mappings": "6bAkBaA,EAAqD,CAChE,MAAO,UACP,eAAgB,UAChB,gBAAiB,UACjB,QAAS,EACT,QAAS,GACT,QAAS,GACT,QAAS,EACT,QAAS,EACT,QAAS,EACT,QAAS,EACX,EAMA,MAAqBC,UAAoCC,EAAAA,cAA2E,CAGlI,YAAYC,EAAkBC,EAAuDC,EAAoB,CACvG,MAAMF,EAAOC,CAAO,EAHtBE,EAAA,iCAKO,KAAA,yBAA2BH,EAAM,UAAUI,EAAAA,WAAY,CAC1D,MAAO,KAAK,QAAQ,MACpB,UAAW,EACX,iBAAkB,GAClB,uBAAwB,GACxB,aAAc,qBACd,sBAAuBC,EAA6B,6BAAA,CAAC,SAAU,IAAK,SAAU,CAAE,CAAA,GAC/EH,CAAS,EAGZ,KAAK,yBAAyB,gBAC5B,IAAII,kBAAgB,CAClB,QAAS,GACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,EAEA,KAAK,yBAAyB,gBAC5B,IAAIA,kBAAgB,CAClB,QAAS,GACT,SAAU,GACV,UAAW,KAAK,QAAQ,eACxB,gBAAiB,KAAK,QAAQ,eAC/B,CAAA,CACH,CAAA,CAGF,mBAAwD,CAC/C,OAAAT,CAAA,CAGT,QAAQU,EAAgD,CAChD,KAAA,CAAE,QAAAC,EAAS,QAAAC,EAAS,QAAAC,EAAS,QAAAC,EAAS,QAAAC,EAAS,QAAAC,GAAY,KAAK,QAEhEC,EAAOP,EAAE,OAAO,KAChBQ,EAAMR,EAAE,OAAO,IACfS,EAAQT,EAAE,OAAO,MAEjBU,EAAY,KAAK,IAAIT,EAASC,EAASC,CAAO,EAG9CQ,EAAaX,EAAE,QAAQO,EAAMG,EAAY,CAAC,EAC1CE,EAAYZ,EAAE,QAAQQ,EAAKE,EAAY,CAAC,EACxCG,EAAcb,EAAE,QAAQS,EAAOC,EAAY,CAAC,EAE9C,GAAA,CAACC,EAAW,WAAA,GAAgB,CAACC,EAAU,cAAgB,CAACC,EAAY,aACtE,OAGF,MAAMC,EAAqB,CAAC,EACtBC,EAAqB,CAAC,EAE5B,QAASC,EAAI,EAAGA,GAAKN,EAAWM,IAAK,CAC7B,MAAAC,EAAcN,EAAW,IAAIK,CAAC,EAC9BE,EAAaN,EAAU,IAAII,CAAC,EAC5BG,EAAeN,EAAY,IAAIG,CAAC,EAChCI,EAAYP,EAAY,IAAIG,EAAI,CAAC,EAEjCK,EAAkB,KAAK,IAAIH,EAAYE,CAAS,EAChDE,EAAmB,KAAK,IAAIL,EAAaG,CAAS,EAElDG,EAAKJ,EAAeE,EACpBG,EAAKF,EAAmBD,EAE9BP,EAAS,KAAKS,CAAE,EAChBR,EAAS,KAAKS,CAAE,CAAA,CAGlB,MAAMC,EAAM,CAACC,EAAeC,IAC1BD,EAAI,MAAM,CAACC,CAAM,EAAE,OAAO,CAACC,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EAEhDC,EAAOL,EAAIX,EAAUb,CAAO,EAAIwB,EAAIV,EAAUd,CAAO,EACrD8B,EAAON,EAAIX,EAAUZ,CAAO,EAAIuB,EAAIV,EAAUb,CAAO,EACrD8B,EAAOP,EAAIX,EAAUX,CAAO,EAAIsB,EAAIV,EAAUZ,CAAO,EAErD8B,EAAc7B,EAAUC,EAAUC,EAGxC,MAAO,CAFI,MAAQF,EAAU0B,EAAOzB,EAAU0B,EAAOzB,EAAU0B,GAAQC,EAEnC,CAAA,CAItC,oBAAqB,CACnB,MAAMC,EAA4C,CAAC,EAEzC,UAAAC,KAAO,KAAK,kBAAkB,KAAM,CAC5C,MAAMC,EAAQD,EAAI,MAClB,GAAG,CAACC,EAAO,SAEX,MAAMC,EAAOF,EAAI,KACjBD,EAAuB,KAAK,CAAC,KAAAG,EAAM,MAAOD,EAAM,CAAC,EAAE,CAAA,CAGhD,KAAA,yBAAyB,QAAQF,CAAsB,CAAA,CAG9D,QAAS,CACP,MAAM,OAAO,EACR,KAAA,MAAM,aAAa,KAAK,wBAAwB,CAAA,CAGvD,eAAgB,CACd,KAAK,yBAAyB,aAAa,CAAC,MAAO,KAAK,QAAQ,MAAM,EACtE,KAAK,mBAAmB,CAAA,CAG1B,aAAavC,EAAmB,CACzB,KAAA,yBAAyB,WAAWA,CAAS,CAAA,CAGpD,cAAuB,CACrB,OAAO,KAAK,yBAAyB,QAAQ,EAAE,UAAU,CAAA,CAE7D"}