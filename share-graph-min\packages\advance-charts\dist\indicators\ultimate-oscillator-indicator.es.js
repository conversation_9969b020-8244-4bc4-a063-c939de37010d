var H = Object.defineProperty;
var T = (n, s, e) => s in n ? H(n, s, { enumerable: !0, configurable: !0, writable: !0, value: e }) : n[s] = e;
var k = (n, s, e) => T(n, typeof s != "symbol" ? s + "" : s, e);
import { LineSeries as U } from "lightweight-charts";
import { ChartIndicator as j } from "./abstract-indicator.es.js";
import { RegionPrimitive as L } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as q } from "../helpers/utils.es.js";
const z = {
  color: "#3179f5",
  // for Ultimate Oscillator
  priceLineColor: "#a1c3ff",
  backgroundColor: "#d2e0fa",
  period1: 7,
  // Short period as per TradingView standard
  period2: 14,
  // Medium period as per TradingView standard
  period3: 28,
  // Long period as per TradingView standard
  weight1: 4,
  // Weight for short period
  weight2: 2,
  // Weight for medium period
  weight3: 1,
  // Weight for long period
  overlay: !1
};
class N extends j {
  constructor(e, i, r) {
    super(e, i);
    k(this, "ultimateOscillatorSeries");
    this.ultimateOscillatorSeries = e.addSeries(U, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "ultimateoscillator",
      autoscaleInfoProvider: q({ maxValue: 100, minValue: 0 })
    }, r), this.ultimateOscillatorSeries.attachPrimitive(
      new L({
        upPrice: 75,
        lowPrice: 70,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    ), this.ultimateOscillatorSeries.attachPrimitive(
      new L({
        upPrice: 30,
        lowPrice: 25,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return z;
  }
  formula(e) {
    const i = this.options.period1, r = this.options.period2, a = this.options.period3, m = this.options.weight1, d = this.options.weight2, g = this.options.weight3, S = e.symbol.high, f = e.symbol.low, v = e.symbol.close, h = Math.max(i, r, a), V = e.new_var(S, h + 1), D = e.new_var(f, h + 1), w = e.new_var(v, h + 1);
    if (!V.calculable() || !D.calculable() || !w.calculable())
      return;
    const b = w.get(1), O = Math.min(f, b), M = Math.max(S, b), A = v - O, R = M - O, P = e.new_var(A, h), C = e.new_var(R, h);
    if (!P.calculable() || !C.calculable())
      return;
    const c = P.getAll(), u = C.getAll();
    let x = 0, I = 0, y = 0;
    if (c.length >= i && u.length >= i) {
      const p = c.slice(-i).reduce((t, o) => t + o, 0), l = u.slice(-i).reduce((t, o) => t + o, 0);
      x = l !== 0 ? p / l : 0;
    } else
      return;
    if (c.length >= r && u.length >= r) {
      const p = c.slice(-r).reduce((t, o) => t + o, 0), l = u.slice(-r).reduce((t, o) => t + o, 0);
      I = l !== 0 ? p / l : 0;
    } else
      return;
    if (c.length >= a && u.length >= a) {
      const p = c.slice(-a).reduce((t, o) => t + o, 0), l = u.slice(-a).reduce((t, o) => t + o, 0);
      y = l !== 0 ? p / l : 0;
    } else
      return;
    const W = m * x + d * I + g * y, _ = m + d + g;
    return [_ !== 0 ? 100 * W / _ : 0];
  }
  applyIndicatorData() {
    const e = [];
    for (const i of this._executionContext.data) {
      const r = i.value;
      if (!r) continue;
      const a = i.time;
      e.push({ time: a, value: r[0] });
    }
    this.ultimateOscillatorSeries.setData(e);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.ultimateOscillatorSeries);
  }
  _applyOptions() {
    this.ultimateOscillatorSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.ultimateOscillatorSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.ultimateOscillatorSeries.getPane().paneIndex();
  }
}
export {
  N as default,
  z as defaultOptions
};
//# sourceMappingURL=ultimate-oscillator-indicator.es.js.map
