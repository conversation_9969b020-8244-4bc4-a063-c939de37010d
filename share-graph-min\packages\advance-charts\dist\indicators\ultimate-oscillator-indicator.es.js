var U = Object.defineProperty;
var j = (l, i, e) => i in l ? U(l, i, { enumerable: !0, configurable: !0, writable: !0, value: e }) : l[i] = e;
var x = (l, i, e) => j(l, typeof i != "symbol" ? i + "" : i, e);
import { LineSeries as q } from "lightweight-charts";
import { ChartIndicator as z } from "./abstract-indicator.es.js";
import { RegionPrimitive as I } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as B } from "../helpers/utils.es.js";
const E = {
  color: "#3179f5",
  // for Ultimate Oscillator
  priceLineColor: "#a1c3ff",
  backgroundColor: "#d2e0fa",
  period1: 7,
  // Short period as per TradingView standard
  period2: 14,
  // Medium period as per TradingView standard
  period3: 28,
  // Long period as per TradingView standard
  weight1: 4,
  // Weight for short period
  weight2: 2,
  // Weight for medium period
  weight3: 1,
  // Weight for long period
  overlay: !1
};
class X extends z {
  constructor(e, s, r) {
    super(e, s);
    x(this, "ultimateOscillatorSeries");
    this.ultimateOscillatorSeries = e.addSeries(q, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "ultimateoscillator",
      autoscaleInfoProvider: B({ maxValue: 100, minValue: 0 })
    }, r), this.ultimateOscillatorSeries.attachPrimitive(
      new I({
        upPrice: 75,
        lowPrice: 70,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    ), this.ultimateOscillatorSeries.attachPrimitive(
      new I({
        upPrice: 30,
        lowPrice: 25,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return E;
  }
  formula(e) {
    const s = this.options.period1, r = this.options.period2, a = this.options.period3, p = this.options.weight1, h = this.options.weight2, m = this.options.weight3, y = e.symbol.high, k = e.symbol.low, d = e.symbol.close, n = Math.max(s, r, a), g = e.new_var(d, 2);
    if (!g.calculable())
      return;
    const S = g.get(1), f = Math.min(k, S), L = Math.max(y, S), V = d - f, D = L - f, v = e.new_var(V, n), b = e.new_var(D, n);
    if (!v.calculable() || !b.calculable())
      return;
    const c = v.getAll(), u = b.getAll();
    if (c.length < n || u.length < n)
      return;
    const _ = c.slice(-s).reduce((t, o) => t + o, 0), w = u.slice(-s).reduce((t, o) => t + o, 0), M = w !== 0 ? _ / w : 0, A = c.slice(-r).reduce((t, o) => t + o, 0), O = u.slice(-r).reduce((t, o) => t + o, 0), R = O !== 0 ? A / O : 0, W = c.slice(-a).reduce((t, o) => t + o, 0), P = u.slice(-a).reduce((t, o) => t + o, 0), H = P !== 0 ? W / P : 0, T = p * M + h * R + m * H, C = p + h + m;
    return [C !== 0 ? 100 * T / C : 0];
  }
  applyIndicatorData() {
    const e = [];
    for (const s of this._executionContext.data) {
      const r = s.value;
      if (!r) continue;
      const a = s.time;
      e.push({ time: a, value: r[0] });
    }
    this.ultimateOscillatorSeries.setData(e);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.ultimateOscillatorSeries);
  }
  _applyOptions() {
    this.ultimateOscillatorSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.ultimateOscillatorSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.ultimateOscillatorSeries.getPane().paneIndex();
  }
}
export {
  X as default,
  E as defaultOptions
};
//# sourceMappingURL=ultimate-oscillator-indicator.es.js.map
