import { useEffect, useMemo, useState } from 'react';
import { useChartContext } from '../context';
import { useAppStore } from '../../../stores/useAppStore';
import { CHART_EVENT_KEYS } from '../../../constants/chartConstant';
import { getFromAndToDateByDataFeed } from '../../../utils/marker';
import {DataManagerPlugin, DataManagerPluginEvents, dayjs} from '@sharegraph-mini/advance-charts';
import EventPoint from './EventPoint';
import { IEarningEvent, IEventPoints } from '../../../types/events';
import EarningHandler from './utils/earning-marker';
import {findNearestPosition} from './utils/findNearestPosition';

const EarningMarker = () => {
  const enableEvents = useAppStore((state) => state.enableEvents);
  const enableEarning = enableEvents[CHART_EVENT_KEYS.EARNING.key];
  const { getChart } = useChartContext();
  const [points, setPoints] = useState<IEventPoints<IEarningEvent>[]>([]);

  const earningInstance = useMemo(() => new EarningHandler(undefined, undefined), [])

  useEffect(() => {
    const chart = getChart();
    if(!chart) return;
    const dataFeed = chart.getPlugin<DataManagerPlugin>(DataManagerPlugin.name)

    const handle = () => {
      const chartData = dataFeed.masterData
      if (chartData.length === 0) return;
      const { fromDate, toDate } = getFromAndToDateByDataFeed(chartData);
      if (enableEarning) {
        earningInstance.load(fromDate, toDate);
      }
    }

    handle()
    return chart.eventBus.subscribe<DataManagerPluginEvents>({
      type: 'dataSetChanged',
      handler: handle
    })
  }, [enableEarning, earningInstance, getChart]);

  useEffect(() => {
    const chart = getChart();
    if (!chart) return;
    const advanceChartApi = chart.api();
    const chartApi = advanceChartApi.chartApi;
    const drawEarning = () => {
      
      const newPoints: IEventPoints<IEarningEvent>[] = [];
      const earningData:IEarningEvent[]  = earningInstance.getData()
      const timeScale = chartApi.timeScale()
      const mainSeries = advanceChartApi.mainSeries;
      if(!mainSeries) return;
     
      earningData.forEach((point) => {
        const xPosi = findNearestPosition(point.dateTime, timeScale, mainSeries);

        if(!xPosi) {
          console.warn('Can not find xPosition for earning point', point)
          return;
        }
        newPoints.push({
          xPosition: xPosi,
          metaData: point,
        });
      });

      setPoints(newPoints.filter((point) => point.xPosition));
    };


    drawEarning();
    chartApi.timeScale().subscribeVisibleLogicalRangeChange(drawEarning);

    earningInstance.dataLoaded.subscribe(drawEarning);

    return () => {
      earningInstance.dataLoaded.unsubscribe(drawEarning);
      chartApi
        .timeScale()
        .unsubscribeVisibleLogicalRangeChange(drawEarning);
    };
  }, [getChart, enableEarning, earningInstance]);

  if (!enableEarning) return null;

  return (
    <>
      {points.map((point, index) => (
        <EventPoint xPosition={point.xPosition} key={index} type="earning">
          <h3 className='earing-marker__title'>{point.metaData.eventName}</h3>
          <time>{dayjs.tz(point.metaData.dateTime, 'UTC').format('MM/DD/YYYY')}</time>
        </EventPoint>
      ))}
    </>
  );
};

export default EarningMarker;
