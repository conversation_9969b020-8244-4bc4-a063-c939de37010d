{"version": 3, "file": "ultimate-oscillator-indicator.es.js", "sources": ["../../src/indicators/ultimate-oscillator-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  period1: number,\n  period2: number,\n  period3: number,\n  weight1: number,\n  weight2: number,\n  weight3: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: UltimateOscillatorIndicatorOptions = {\n  color: \"#3179f5\",     // for Ultimate Oscillator\n  priceLineColor: \"#a1c3ff\",\n  backgroundColor: '#d2e0fa',\n  period1: 7,      // Short period as per TradingView standard\n  period2: 14,     // Medium period as per TradingView standard\n  period3: 28,     // Long period as per TradingView standard\n  weight1: 4,      // Weight for short period\n  weight2: 2,      // Weight for medium period\n  weight3: 1,      // Weight for long period\n  overlay: false\n}\n\nexport type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>\n\nexport type UltimateOscillatorData = [UltimateOscillatorLine]\n\nexport default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {\n  ultimateOscillatorSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.ultimateOscillatorSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'ultimateoscillator',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    // Add region primitives for visual reference at 30 and 70 thresholds\n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 75,\n        lowPrice: 70,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n    \n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 30,\n        lowPrice: 25,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): UltimateOscillatorIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): UltimateOscillatorData | undefined {\n    const { period1, period2, period3, weight1, weight2, weight3 } = this.options;\n  \n    const high = c.symbol.high;\n    const low = c.symbol.low;\n    const close = c.symbol.close;\n  \n    const maxPeriod = Math.max(period1, period2, period3);\n  \n    // Lấy dữ liệu giá cần thiết\n    const highSeries = c.new_var(high, maxPeriod + 1);\n    const lowSeries = c.new_var(low, maxPeriod + 1);\n    const closeSeries = c.new_var(close, maxPeriod + 1);\n  \n    if (!highSeries.calculable() || !lowSeries.calculable() || !closeSeries.calculable()) {\n      return;\n    }\n  \n    const bpSeries: number[] = [];\n    const trSeries: number[] = [];\n  \n    for (let i = 1; i <= maxPeriod; i++) {\n      const currentHigh = highSeries.get(i);\n      const currentLow = lowSeries.get(i);\n      const currentClose = closeSeries.get(i);\n      const prevClose = closeSeries.get(i - 1);\n  \n      const minLowPrevClose = Math.min(currentLow, prevClose);\n      const maxHighPrevClose = Math.max(currentHigh, prevClose);\n  \n      const bp = currentClose - minLowPrevClose;\n      const tr = maxHighPrevClose - minLowPrevClose;\n  \n      bpSeries.push(bp);\n      trSeries.push(tr);\n    }\n  \n    const sum = (arr: number[], period: number) =>\n      arr.slice(-period).reduce((acc, val) => acc + val, 0);\n  \n    const avg1 = sum(bpSeries, period1) / sum(trSeries, period1);\n    const avg2 = sum(bpSeries, period2) / sum(trSeries, period2);\n    const avg3 = sum(bpSeries, period3) / sum(trSeries, period3);\n  \n    const totalWeight = weight1 + weight2 + weight3;\n    const uo = 100 * ((weight1 * avg1 + weight2 * avg2 + weight3 * avg3) / totalWeight);\n  \n    return [uo as UltimateOscillatorLine];\n  }\n  \n\n  applyIndicatorData() {\n    const ultimateOscillatorData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      ultimateOscillatorData.push({time, value: value[0]});\n    }\n\n    this.ultimateOscillatorSeries.setData(ultimateOscillatorData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.ultimateOscillatorSeries);\n  }\n\n  _applyOptions() {\n    this.ultimateOscillatorSeries.applyOptions({color: this.options.color});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.ultimateOscillatorSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.ultimateOscillatorSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "UltimateOscillatorIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period1", "period2", "period3", "weight1", "weight2", "weight3", "high", "low", "close", "max<PERSON><PERSON><PERSON>", "highSeries", "lowSeries", "closeSeries", "bpSeries", "trSeries", "i", "currentHigh", "currentLow", "currentClose", "prevClose", "minLowPrevClose", "maxHighPrevClose", "bp", "tr", "sum", "arr", "period", "acc", "val", "avg1", "avg2", "avg3", "totalWeight", "ultimateOscillatorData", "bar", "value", "time"], "mappings": ";;;;;;;AAkBO,MAAMA,IAAqD;AAAA,EAChE,OAAO;AAAA;AAAA,EACP,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AACX;AAMA,MAAqBC,UAAoCC,EAA2E;AAAA,EAGlI,YAAYC,GAAkBC,GAAuDC,GAAoB;AACvG,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAKO,SAAA,2BAA2BH,EAAM,UAAUI,GAAY;AAAA,MAC1D,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,KAAK,UAAU,EAAE,CAAA;AAAA,OAC/EH,CAAS,GAGZ,KAAK,yBAAyB;AAAA,MAC5B,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH,GAEA,KAAK,yBAAyB;AAAA,MAC5B,IAAIA,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAAwD;AAC/C,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAgD;AAChD,UAAA,EAAE,SAAAC,GAAS,SAAAC,GAAS,SAAAC,GAAS,SAAAC,GAAS,SAAAC,GAAS,SAAAC,MAAY,KAAK,SAEhEC,IAAOP,EAAE,OAAO,MAChBQ,IAAMR,EAAE,OAAO,KACfS,IAAQT,EAAE,OAAO,OAEjBU,IAAY,KAAK,IAAIT,GAASC,GAASC,CAAO,GAG9CQ,IAAaX,EAAE,QAAQO,GAAMG,IAAY,CAAC,GAC1CE,IAAYZ,EAAE,QAAQQ,GAAKE,IAAY,CAAC,GACxCG,IAAcb,EAAE,QAAQS,GAAOC,IAAY,CAAC;AAE9C,QAAA,CAACC,EAAW,WAAA,KAAgB,CAACC,EAAU,gBAAgB,CAACC,EAAY;AACtE;AAGF,UAAMC,IAAqB,CAAC,GACtBC,IAAqB,CAAC;AAE5B,aAASC,IAAI,GAAGA,KAAKN,GAAWM,KAAK;AAC7B,YAAAC,IAAcN,EAAW,IAAIK,CAAC,GAC9BE,IAAaN,EAAU,IAAII,CAAC,GAC5BG,IAAeN,EAAY,IAAIG,CAAC,GAChCI,IAAYP,EAAY,IAAIG,IAAI,CAAC,GAEjCK,IAAkB,KAAK,IAAIH,GAAYE,CAAS,GAChDE,IAAmB,KAAK,IAAIL,GAAaG,CAAS,GAElDG,IAAKJ,IAAeE,GACpBG,IAAKF,IAAmBD;AAE9B,MAAAP,EAAS,KAAKS,CAAE,GAChBR,EAAS,KAAKS,CAAE;AAAA,IAAA;AAGlB,UAAMC,IAAM,CAACC,GAAeC,MAC1BD,EAAI,MAAM,CAACC,CAAM,EAAE,OAAO,CAACC,GAAKC,MAAQD,IAAMC,GAAK,CAAC,GAEhDC,IAAOL,EAAIX,GAAUb,CAAO,IAAIwB,EAAIV,GAAUd,CAAO,GACrD8B,IAAON,EAAIX,GAAUZ,CAAO,IAAIuB,EAAIV,GAAUb,CAAO,GACrD8B,IAAOP,EAAIX,GAAUX,CAAO,IAAIsB,EAAIV,GAAUZ,CAAO,GAErD8B,IAAc7B,IAAUC,IAAUC;AAGxC,WAAO,CAFI,QAAQF,IAAU0B,IAAOzB,IAAU0B,IAAOzB,IAAU0B,KAAQC,EAEnC;AAAA,EAAA;AAAA,EAItC,qBAAqB;AACnB,UAAMC,IAA4C,CAAC;AAEzC,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI;AAClB,UAAG,CAACC,EAAO;AAEX,YAAMC,IAAOF,EAAI;AACjB,MAAAD,EAAuB,KAAK,EAAC,MAAAG,GAAM,OAAOD,EAAM,CAAC,GAAE;AAAA,IAAA;AAGhD,SAAA,yBAAyB,QAAQF,CAAsB;AAAA,EAAA;AAAA,EAG9D,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,wBAAwB;AAAA,EAAA;AAAA,EAGvD,gBAAgB;AACd,SAAK,yBAAyB,aAAa,EAAC,OAAO,KAAK,QAAQ,OAAM,GACtE,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,aAAavC,GAAmB;AACzB,SAAA,yBAAyB,WAAWA,CAAS;AAAA,EAAA;AAAA,EAGpD,eAAuB;AACrB,WAAO,KAAK,yBAAyB,QAAQ,EAAE,UAAU;AAAA,EAAA;AAE7D;"}