// import { Time } from "lightweight-charts";
import { PluginManager } from "./advance-chart/core/plugin-manager";
import { ChartCorePlugin } from "./advance-chart/plugins/chart-core-plugin";
import { ChartTypePlugin } from "./advance-chart/plugins/chart-type-plugin";
import { DataManagerPlugin } from "./advance-chart/plugins/data-manager-plugin";
import { OHLCVSimple } from "./interface";
import {
  IDataFetch,
  IDataFetchQuery,
  Interval,
  Period,
} from "./advance-charts";
import { DataFeedPlugin } from "./advance-chart/plugins/data-feed-plugin";
import { IndicatorManagerPlugin } from "./advance-chart/plugins/indicator-manager-plugin";
import { UpDownPricePlugin } from "./advance-chart/plugins/up-down-price-plugin";

class DataFetch implements IDataFetch {
  async sleep(num: number) {
    return new Promise((res) => setTimeout(res, num));
  }

  async fakeData(from: Date, to: Date, interval = "1d") {
    const response = await fetch(
      `https://www.binance.com/api/v3/uiKlines?limit=1000&symbol=BTCUSDT&interval=${interval}&startTime=${from.getTime()}&endTime=${to.getTime()}`
    );
    const data = await response.json();

    const result = data.map((item: [number, string, string, string, string, string]) => {
      const [time, open, high, low, close, volume] = item;
      return {
        time: Math.round(time / 1000),
        open: parseFloat(open),
        high: parseFloat(high),
        low: parseFloat(low),
        close: parseFloat(close),
        volume: parseFloat(volume),
      };
    });

    return result as OHLCVSimple[];
  }

  getInterval(interval: Interval) {
    switch (interval.period) {
      case "day":
        return interval.times + "d";
      case "minute":
        return interval.times + "m";
      case "hour":
        return interval.times + "h";
      case "week":
        return interval.times + "w";
      case "month":
        return interval.times + "d";
      default:
        return interval.times + "d";
    }
  }

  async fetchInitialData(param: IDataFetchQuery): Promise<OHLCVSimple[]> {
    return this.fakeData(
      param.from,
      param.to,
      this.getInterval(param.interval)
    );
  }

  async fetchPaginationData(param: IDataFetchQuery): Promise<OHLCVSimple[]> {
    return this.fakeData(
      param.from,
      param.to,
      this.getInterval(param.interval)
    );
  }

  async fetchUpdateData(param: IDataFetchQuery): Promise<OHLCVSimple[]> {
    return this.fakeData(
      param.from,
      param.to,
      this.getInterval(param.interval)
    );
  }
}

const container = document.createElement("div");
document.body.appendChild(container);

// const generateSampleData = (days: number = 100): OHLCVSimple[] => {
//   const data: OHLCVSimple[] = [];
//   let basePrice = 100;
//   const startTime = new Date("2024-01-01").getTime() / 1000;

//   for (let i = 0; i < days; i++) {
//     const time = startTime + i * 24 * 60 * 60; // Daily data
//     const volatility = 0.02;
//     const change = (Math.random() - 0.5) * volatility * basePrice;

//     const open = basePrice;
//     const close = basePrice + change;
//     const high = Math.max(open, close) + Math.random() * 0.01 * basePrice;
//     const low = Math.min(open, close) - Math.random() * 0.01 * basePrice;
//     const volume = Math.floor(Math.random() * 1000000) + 100000;

//     data.push({
//       time: time as Time,
//       open,
//       high,
//       low,
//       close,
//       volume,
//     });

//     basePrice = close;
//   }

//   return data;
// };

const pluginManager = new PluginManager();
pluginManager.registerPlugin(
  ChartCorePlugin.name,
  new ChartCorePlugin(container)
);
pluginManager.registerPlugin(ChartTypePlugin.name, new ChartTypePlugin());
pluginManager.registerPlugin(DataManagerPlugin.name, new DataManagerPlugin());
pluginManager.registerPlugin(
  DataFeedPlugin.name,
  new DataFeedPlugin(new DataFetch())
);
pluginManager.registerPlugin(
  IndicatorManagerPlugin.name,
  new IndicatorManagerPlugin()
);
pluginManager.registerPlugin(UpDownPricePlugin.name, new UpDownPricePlugin());

pluginManager
  .getPlugin<ChartTypePlugin>(ChartTypePlugin.name)
  .setChartType("candle");

pluginManager
  .getPlugin<DataFeedPlugin>(DataFeedPlugin.name)
  .setRange({
    from: new Date("2024-01-01"),
    to: new Date(),
    interval: { period: Period.day, times: 1 },
  });

const indicatorPlugin = pluginManager.getPlugin<IndicatorManagerPlugin>(
  IndicatorManagerPlugin.name
);

indicatorPlugin.showVolume("volume_overlay");
indicatorPlugin.addIndicator("bb");
indicatorPlugin.addIndicator("macd");
indicatorPlugin.addIndicator("rsi");
