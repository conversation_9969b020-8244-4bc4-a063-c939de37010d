export type IEventHandler<T = unknown> = (payload: T) => void;

export type IEventDefinition<T extends Record<string, unknown> = Record<string, unknown>> = T;

export type IEventFire<T extends Record<string, unknown>> = {
  [K in keyof T]: T[K] extends undefined ? {type: K} : {type: K, payload: T[K]};
}[keyof T];

export type IEventSubscribe<T extends Record<string, unknown>> = {
  [K in keyof T]: {
    type: K;
    handler: IEventHandler<T[K]>;
  };
}[keyof T];

export interface IEventBus {
  fire<T extends IEventDefinition>(params: IEventFire<T>): void;
  subscribe<T extends IEventDefinition>(params: IEventSubscribe<T>, linkedObject?: unknown, singleshot?: boolean): () => void;
  unsubscribe<T extends IEventDefinition>(params: IEventSubscribe<T>): void;
  removeAllListeners(event?: string): void;
  destroy(): void;
}

export interface IPluginContext {
  eventBus: IEventBus;
  core: IPluginManager;
}

export interface IPlugin {
  initialize(context: IPluginContext): void;
  destroy(): void;
}

export interface IPluginManager {
  eventBus: IEventBus;
  getPlugin<T extends IPlugin>(name: string): T;
}