var W = Object.defineProperty;
var R = (s, t, e) => t in s ? W(s, t, { enumerable: !0, configurable: !0, writable: !0, value: e }) : s[t] = e;
var P = (s, t, e) => R(s, typeof t != "symbol" ? t + "" : t, e);
import { LineSeries as T } from "lightweight-charts";
import { ChartIndicator as U } from "./abstract-indicator.es.js";
import { RegionPrimitive as O } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as j } from "../helpers/utils.es.js";
const q = {
  color: "#3179f5",
  // for Ultimate Oscillator
  priceLineColor: "#a1c3ff",
  backgroundColor: "#d2e0fa",
  period1: 7,
  // Short period as per TradingView standard
  period2: 14,
  // Medium period as per TradingView standard
  period3: 28,
  // Long period as per TradingView standard
  weight1: 4,
  // Weight for short period
  weight2: 2,
  // Weight for medium period
  weight3: 1,
  // Weight for long period
  overlay: !1
};
class J extends U {
  constructor(e, o, i) {
    super(e, o);
    P(this, "ultimateOscillatorSeries");
    this.ultimateOscillatorSeries = e.addSeries(T, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "ultimateoscillator",
      autoscaleInfoProvider: j({ maxValue: 100, minValue: 0 })
    }, i), this.ultimateOscillatorSeries.attachPrimitive(
      new O({
        upPrice: 75,
        lowPrice: 70,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    ), this.ultimateOscillatorSeries.attachPrimitive(
      new O({
        upPrice: 30,
        lowPrice: 25,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return q;
  }
  formula(e) {
    const { period1: o, period2: i, period3: l, weight1: g, weight2: f, weight3: v } = this.options, x = e.symbol.high, I = e.symbol.low, y = e.symbol.close, n = Math.max(o, i, l), S = e.new_var(x, n + 1), w = e.new_var(I, n + 1), p = e.new_var(y, n + 1);
    if (!S.calculable() || !w.calculable() || !p.calculable())
      return;
    const c = [], u = [];
    for (let r = 1; r <= n; r++) {
      const h = S.get(r), m = w.get(r), d = p.get(r), C = p.get(r - 1), b = Math.min(m, C), M = Math.max(h, C), V = d - b, H = M - b;
      c.push(V), u.push(H);
    }
    const a = (r, h) => r.slice(-h).reduce((m, d) => m + d, 0), L = a(c, o) / a(u, o), k = a(c, i) / a(u, i), D = a(c, l) / a(u, l), _ = g + f + v;
    return [100 * ((g * L + f * k + v * D) / _)];
  }
  applyIndicatorData() {
    const e = [];
    for (const o of this._executionContext.data) {
      const i = o.value;
      if (!i) continue;
      const l = o.time;
      e.push({ time: l, value: i[0] });
    }
    this.ultimateOscillatorSeries.setData(e);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.ultimateOscillatorSeries);
  }
  _applyOptions() {
    this.ultimateOscillatorSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.ultimateOscillatorSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.ultimateOscillatorSeries.getPane().paneIndex();
  }
}
export {
  J as default,
  q as defaultOptions
};
//# sourceMappingURL=ultimate-oscillator-indicator.es.js.map
