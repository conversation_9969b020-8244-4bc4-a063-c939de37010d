var C = Object.defineProperty;
var y = (t, o, e) => o in t ? C(t, o, { enumerable: !0, configurable: !0, writable: !0, value: e }) : t[o] = e;
var v = (t, o, e) => y(t, typeof o != "symbol" ? o + "" : o, e);
import { LineSeries as A } from "lightweight-charts";
import { ChartIndicator as M } from "./abstract-indicator.es.js";
import { RegionPrimitive as V } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as _ } from "../helpers/utils.es.js";
const D = {
  color: "rgba(255, 152, 0, 1)",
  // Orange for Mass Index
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#ff98001a",
  emaPeriod: 9,
  // 9-period EMA as per TradingView standard
  sumPeriod: 25,
  // 25-period sum as per TradingView standard
  overlay: !1
};
class H extends M {
  constructor(e, r, s) {
    super(e, r);
    v(this, "massIndexSeries");
    this.massIndexSeries = e.addSeries(A, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "massindex",
      autoscaleInfoProvider: _({ maxValue: 30, minValue: 20 })
    }, s), this.massIndexSeries.attachPrimitive(
      new V({
        upPrice: 27.5,
        lowPrice: 26.5,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return D;
  }
  formula(e) {
    const r = this.options.emaPeriod, s = this.options.sumPeriod, n = e.symbol.high, c = e.symbol.low, d = r * 2 + s, g = e.new_var(n, d), S = e.new_var(c, d);
    if (!g.calculable() || !S.calculable())
      return;
    const m = n - c, l = 2 / (r + 1), u = e.new_var(0, 2), p = u.get(1);
    let i;
    isNaN(p) ? i = m : i = l * m + (1 - l) * p, u.set(i);
    const h = e.new_var(0, 2), f = h.get(1);
    let a;
    isNaN(f) ? a = i : a = l * i + (1 - l) * f, h.set(a);
    const P = a !== 0 ? i / a : 1, I = e.new_var(P, s + 1);
    if (!I.calculable())
      return;
    const x = I.getAll();
    return x.length < s ? void 0 : [x.slice(-s).reduce((b, w) => b + w, 0)];
  }
  applyIndicatorData() {
    const e = [];
    for (const r of this._executionContext.data) {
      const s = r.value;
      if (!s) continue;
      const n = r.time;
      e.push({ time: n, value: s[0] });
    }
    this.massIndexSeries.setData(e);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.massIndexSeries);
  }
  _applyOptions() {
    this.massIndexSeries.applyOptions({ color: this.options.color }), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.massIndexSeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.massIndexSeries.getPane().paneIndex();
  }
}
export {
  H as default,
  D as defaultOptions
};
//# sourceMappingURL=mass-index-indicator.es.js.map
