import { AdvanceChart, PriceChannelIndicator } from "@sharegraph-mini/advance-charts";
import {
  BBIndicator,
  DMIIndicator,
  EMAIndicator,
  MACDIndicator,
  MomentumIndicator,
  RSIIndicator,
  SMAIndicator,
  VolumeIndicator,
  WMAIndicator,
  WilliamsIndicator,
  MassIndexIndicator,
  UltimateOscillatorIndicator,
  VROCIndicator,
  ChaikinsVolatilityIndicator
} from "@sharegraph-mini/advance-charts";
import { ChartIndicator } from "@sharegraph-mini/advance-charts";
import {StochasticIndicator} from "@sharegraph-mini/advance-charts";
import { Legend } from './Legend';

export default function renderLegend(
  indicator: ChartIndicator,
  index: number | string,
  advanceChart: AdvanceChart
) {
  if (indicator instanceof VolumeIndicator)
    return (
      <Legend
        name="Volume"
        indicator={indicator}
        key={index}
        renderer={(d) =>
          d && d.value ? (
            <span style={{ color: d.value[1] ? indicator.options.upColor : indicator.options.downColor }}>
              {advanceChart.api().numberFormatter.volume(d.value[0])}
            </span>
          ) : null
        }
      />
    );

  if (indicator instanceof RSIIndicator)
    return (
      <Legend
        name="RSI"
        indicator={indicator}
        key={index}
        renderer={(d) =>
          d && d.value ? (
            <span style={{ color: indicator.options.color }}>
              {advanceChart.api().numberFormatter.decimal(d.value[0])}
            </span>
          ) : null
        }
      />
    );

  if (indicator instanceof MACDIndicator)
    return (
      <Legend
        name="MACD"
        indicator={indicator}
        key={index}
        renderer={(d) => d && d.value ? (
          <>
            <span
              style={{
                color:
                  (d.value[2] ?? 0) > 0
                    ? indicator.options.upColor
                    : indicator.options.downColor,
              }}
            >
              {advanceChart.api().numberFormatter.decimal(d.value[2])}
            </span>{' '}
            <span style={{ color: indicator.options.macdLineColor }}>
              {advanceChart.api().numberFormatter.decimal(d.value[0])}
            </span>{' '}
            <span
              style={{
                color: indicator.options.signalLineColor,
              }}
            >
              {advanceChart.api().numberFormatter.decimal(d.value[1])}
            </span>
          </>
        ) : null}
      />
    );

  if (indicator instanceof BBIndicator)
    return (
      <Legend
        name="BB"
        indicator={indicator}
        key={index}
        renderer={(d) =>
          d && d.value ? (
            <>
              {' '}
              <span style={{ color: indicator.options.middleLineColor }}>
                {advanceChart.api().numberFormatter.decimal(d.value[1])}
              </span>{' '}
              <span style={{ color: indicator.options.upperLineColor }}>
                {advanceChart.api().numberFormatter.decimal(d.value[0])}
              </span>{' '}
              <span style={{ color: indicator.options.lowerLineColor }}>
                {advanceChart.api().numberFormatter.decimal(d.value[2])}
              </span>{' '}
            </>
          ) : null
        }
      />
    );

  if (indicator instanceof StochasticIndicator)
    return (
      <Legend
        name="Stoch"
        indicator={indicator}
        key={index}
        renderer={(d) => d && d.value && (
          <>
            {' '}
            <span style={{ color: indicator.options.color }}>
              {advanceChart.api().numberFormatter.decimal(d.value[1])}
            </span>{' '}
            <span style={{ color: indicator.options.signalColor }}>
              {advanceChart.api().numberFormatter.decimal(d.value[0])}
            </span>
          </>
        )}
      />
    );

  if (indicator instanceof SMAIndicator)
    return (
      <Legend
        name="SMA"
        indicator={indicator}
        key={index}
        renderer={(d) =>
          d ? (
            <>
              {' '}
              <span style={{ color: indicator.options.color }}>
                {advanceChart.api().numberFormatter.decimal(d.value.at(0))}
              </span>{' '}
            </>
          ) : null
        }
      />
    );

  if (indicator instanceof EMAIndicator)
    return (
      <Legend
        name="EMA"
        indicator={indicator}
        key={index}
        renderer={(d) =>
          d && d.value ? (
            <>
              {' '}
              <span style={{ color: indicator.options.color }}>
                {advanceChart.api().numberFormatter.decimal(d.value[0])}
              </span>{' '}
            </>
          ) : null
        }
      />
    );

  if (indicator instanceof WMAIndicator)
    return (
      <Legend
        name="WMA"
        indicator={indicator}
        key={index}
        renderer={(d) =>
          d && d.value ? (
            <>
              {' '}
              <span style={{ color: indicator.options.color }}>
                {advanceChart.api().numberFormatter.decimal(d.value[0])}
              </span>{' '}
            </>
          ) : null
        }
      />
    );

  if (indicator instanceof MomentumIndicator)
    return (
      <Legend
        name={`Momentum(${indicator.options.period})`}
        indicator={indicator}
        key={index}
        renderer={(d) => d && d.value ? (
          <>
            <span style={{ color: indicator.options.color }}>
              {indicator.options.usePercentage
                ? `${advanceChart.api().numberFormatter.decimal(d.value[0])}%`
                : advanceChart.api().numberFormatter.decimal(d.value[0])
              }
            </span>
          </>
        ) : null}
      />
    );

  if (indicator instanceof WilliamsIndicator)
    return (
      <Legend
        name="Williams %R"
        indicator={indicator}
        key={index}
        renderer={(d) =>
          d && d.value ? (
            <span style={{ color: indicator.options.color }}>
              {advanceChart.api().numberFormatter.decimal(d.value[0])}
            </span>
          ) : null
        }
      />
    );

  if (indicator instanceof DMIIndicator)
    return (
      <Legend
        name="DMI"
        indicator={indicator}
        key={index}
        renderer={(d) => d && d.value ? (
          <>
            <span style={{ color: indicator.options.adxColor }}>
              ADX: {advanceChart.api().numberFormatter.decimal(d.value[0])}
            </span>{' '}
            <span style={{ color: indicator.options.plusDIColor }}>
              +DI: {advanceChart.api().numberFormatter.decimal(d.value[1])}
            </span>{' '}
            <span style={{ color: indicator.options.minusDIColor }}>
              -DI: {advanceChart.api().numberFormatter.decimal(d.value[2])}
            </span>
          </>
        ) : null}
      />
    );

  if (indicator instanceof MassIndexIndicator)
    return (
      <Legend
        name="Mass Index"
        indicator={indicator}
        key={index}
        renderer={(d) =>
          d && d.value ? (
            <span style={{ color: indicator.options.color }}>
              {advanceChart.api().numberFormatter.decimal(d.value[0])}
            </span>
          ) : null
        }
      />
    );

  if(indicator instanceof UltimateOscillatorIndicator)
    return (
      <Legend
        name="Ultimate Oscillator"
        indicator={indicator}
        key={index}
        renderer={(d) =>
          d && d.value ? (
            <span style={{ color: indicator.options.color }}>
              {advanceChart.api().numberFormatter.decimal(d.value[0])}
            </span>
          ) : null
        }
      />
    );

  if(indicator instanceof VROCIndicator)
    return (
      <Legend
        name={`VROC(${indicator.options.period})`}
        indicator={indicator}
        key={index}
        renderer={(d) => d && d.value ? (
          <>
            <span style={{ color: indicator.options.color }}>
              {advanceChart.api().numberFormatter.decimal(d.value[0])}
            </span>
          </>
        ) : null}
      />
    );

  if(indicator instanceof ChaikinsVolatilityIndicator)
    return (
      <Legend
        name={`Chaikin Volatility Strategy(${indicator.options.period})`}
        indicator={indicator}
        key={index}
        renderer={(d) => d && d.value ? (
          <>
            <span style={{ color: indicator.options.color }}>
              {advanceChart.api().numberFormatter.decimal(d.value[0])}
            </span>
          </>
        ) : null}
      />
    );
    if(indicator instanceof PriceChannelIndicator)
    return (
      <Legend
        name="Price Channel"
        indicator={indicator}
        key={index}
        renderer={(d) => d && d.value ? (
          <>
            <span style={{ color: indicator.options.upperLineColor }}>
              Upper: {advanceChart.api().numberFormatter.decimal(d.value[0])}
            </span>
            {indicator.options.showMiddleLine && (
              <>
                {' | '}
                <span style={{ color: indicator.options.middleLineColor }}>
                  Middle: {advanceChart.api().numberFormatter.decimal(d.value[1])}
                </span>
              </>
            )}
            {' | '}
            <span style={{ color: indicator.options.lowerLineColor }}>
              Lower: {advanceChart.api().numberFormatter.decimal(d.value[2])}
            </span>
          </>
        ) : null}
      />
    );
}
