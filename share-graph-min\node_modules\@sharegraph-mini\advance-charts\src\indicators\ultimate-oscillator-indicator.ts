import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from "lightweight-charts";
import {ChartIndicator, ChartIndicatorOptions} from "./abstract-indicator";
import {RegionPrimitive} from "../custom-primitive/primitive/region";
import {autoScaleInfoProviderCreator} from "../helpers/utils";
import {Context} from "../helpers/execution-indicator";

export interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {
  color: string,
  period1: number,
  period2: number,
  period3: number,
  weight1: number,
  weight2: number,
  weight3: number,
  priceLineColor: string,
  backgroundColor: string
}

export const defaultOptions: UltimateOscillatorIndicatorOptions = {
  color: "#3179f5",     // for Ultimate Oscillator
  priceLineColor: "#a1c3ff",
  backgroundColor: '#d2e0fa',
  period1: 7,      // Short period as per TradingView standard
  period2: 14,     // Medium period as per TradingView standard
  period3: 28,     // Long period as per TradingView standard
  weight1: 4,      // Weight for short period
  weight2: 2,      // Weight for medium period
  weight3: 1,      // Weight for long period
  overlay: false
}

export type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>

export type UltimateOscillatorData = [UltimateOscillatorLine]

export default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {
  ultimateOscillatorSeries: ISeriesApi<SeriesType>

  constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number) {
    super(chart, options)
    
    this.ultimateOscillatorSeries = chart.addSeries(LineSeries, {
      color: this.options.color,
      lineWidth: 2,
      priceLineVisible: false,
      crosshairMarkerVisible: false,
      priceScaleId: 'ultimateoscillator',
      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})
    }, paneIndex);
    
    // Add region primitives for visual reference at 30 and 70 thresholds
    this.ultimateOscillatorSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 75,
        lowPrice: 70,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
    
    this.ultimateOscillatorSeries.attachPrimitive(
      new RegionPrimitive({
        upPrice: 30,
        lowPrice: 25,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }

  getDefaultOptions(): UltimateOscillatorIndicatorOptions {
    return defaultOptions
  }

  formula(c: Context): UltimateOscillatorData | undefined {
    // 1. Khởi tạo tham số
    const period1 = this.options.period1;  // Mặc định: 7
    const period2 = this.options.period2;  // Mặc định: 14
    const period3 = this.options.period3;  // Mặc định: 28
    const weight1 = this.options.weight1;  // Mặc định: 4
    const weight2 = this.options.weight2;  // Mặc định: 2
    const weight3 = this.options.weight3;  // Mặc định: 1

    // 2. Lấy dữ liệu giá
    const high = c.symbol.high;
    const low = c.symbol.low;
    const close = c.symbol.close;

    // 3. Tính toán dữ liệu cần thiết
    const maxPeriod = Math.max(period1, period2, period3);
    
    // Lấy giá đóng cửa phiên trước
    const prevCloseSeries = c.new_var(close, 2);
    if (!prevCloseSeries.calculable()) return;
    const prevClose = prevCloseSeries.get(1);

    // 4. Tính Buying Pressure (BP) và True Range (TR)
    const buyingPressure = close - Math.min(low, prevClose);
    const trueRange = Math.max(high, prevClose) - Math.min(low, prevClose);

    // 5. Lưu trữ lịch sử BP và TR
    const bpSeries = c.new_var(buyingPressure, maxPeriod);
    const trSeries = c.new_var(trueRange, maxPeriod);
    
    if (!bpSeries.calculable() || !trSeries.calculable()) return;

    // 6. Tính toán tỷ lệ BP/TR cho từng phiên
    const bpValues = bpSeries.get_last(maxPeriod);
    const trValues = trSeries.get_last(maxPeriod);
    
    const ratios: number[] = [];
    for (let i = 0; i < bpValues.length; i++) {
        const tr = trValues[i];
        ratios.push(tr > 0 ? bpValues[i] / tr : 0);
    }

    // 7. Tính trung bình cho từng chu kỳ
    const calculateAverage = (period: number) => {
        const count = Math.min(period, ratios.length);
        if (count === 0) return 0;
        return ratios.slice(-count).reduce((sum, val) => sum + val, 0) / count;
    };

    const avg1 = calculateAverage(period1);
    const avg2 = calculateAverage(period2);
    const avg3 = calculateAverage(period3);

    // 8. Tính Ultimate Oscillator
    const weightedSum = (weight1 * avg1) + (weight2 * avg2) + (weight3 * avg3);
    const totalWeight = weight1 + weight2 + weight3;
    const ultimateOscillator = totalWeight > 0 ? 100 * (weightedSum / totalWeight) : 0;

    // 9. Xử lý giá trị không hợp lệ
    if (!isFinite(ultimateOscillator)) {
        return [0 as UltimateOscillatorLine];
    }

    return [ultimateOscillator as UltimateOscillatorLine];
}
  

  applyIndicatorData() {
    const ultimateOscillatorData: SingleValueData[] = [];
    
    for(const bar of this._executionContext.data) {
      const value = bar.value;
      if(!value) continue;
      
      const time = bar.time as Time;
      ultimateOscillatorData.push({time, value: value[0]});
    }

    this.ultimateOscillatorSeries.setData(ultimateOscillatorData);
  }

  remove() {
    super.remove()
    this.chart.removeSeries(this.ultimateOscillatorSeries);
  }

  _applyOptions() {
    this.ultimateOscillatorSeries.applyOptions({color: this.options.color});
    this.applyIndicatorData();
  }

  setPaneIndex(paneIndex: number) {
    this.ultimateOscillatorSeries.moveToPane(paneIndex);
  }

  getPaneIndex(): number {
    return this.ultimateOscillatorSeries.getPane().paneIndex();
  }
}
