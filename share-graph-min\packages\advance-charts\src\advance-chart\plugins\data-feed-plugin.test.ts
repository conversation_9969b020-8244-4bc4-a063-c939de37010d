import { expect, it, describe, vi, beforeEach } from 'vitest';
import { DataFeedPlugin, IDataFetch } from './data-feed-plugin';
import { Logical, Time } from 'lightweight-charts';
import { Period } from '../i-advance-chart';
import { timeToDayjs } from '../../helpers/utils';
import { ChartCorePlugin } from './chart-core-plugin';
import { DataManagerPlugin } from './data-manager-plugin';

describe('DataFeedPlugin', () => {
  // Test data helpers (reusing pattern from data-feed.test.ts)
  const sampleData = [
    { time: 1672531200 as Time, open: 100, high: 110, low: 90, close: 105, volume: 1000 }, // 2023-01-01
    { time: 1672617600 as Time, open: 105, high: 115, low: 95, close: 110, volume: 1200 }, // 2023-01-02
  ];

  const sampleRange = {
    from: new Date('2023-01-01'),
    to: new Date('2023-01-02'),
    interval: { period: Period.day, times: 1 },
  };

  // Helper to create mock data fetcher (reusing pattern)
  const createMockDataFetch = (overrides = {}): IDataFetch => ({
    fetchInitialData: vi.fn().mockResolvedValue(sampleData),
    fetchPaginationData: vi.fn().mockResolvedValue([]),
    fetchUpdateData: vi.fn().mockResolvedValue([]),
    refeshTime: 5000,
    ...overrides
  });

  // Helper to create mock chart core plugin
  const createMockChartCore = (overrides = {}) => ({
    chartApi: {
      timeScale: () => ({
        subscribeVisibleLogicalRangeChange: vi.fn(),
        unsubscribeVisibleLogicalRangeChange: vi.fn(),
        getVisibleLogicalRange: vi.fn().mockReturnValue({
          from: 0 as Logical,
          to: 100 as Logical
        }),
        timeToIndex: vi.fn().mockReturnValue(50),
      }),
    },
    fitRange: vi.fn(),
    ...overrides
  });

  // Helper to create mock data manager plugin
  const createMockDataManager = (overrides = {}) => ({
    masterData: sampleData,
    interval: { period: Period.day, times: 1 },
    setMasterData: vi.fn(),
    updateMasterData: vi.fn().mockReturnValue(true),
    ...overrides
  });

  // Helper to create mock plugin context
  const createMockContext = (chartCore = createMockChartCore(), dataManager = createMockDataManager()) => ({
    eventBus: {
      fire: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      removeAllListeners: vi.fn(),
      destroy: vi.fn(),
    },
    core: {
      getPlugin: vi.fn().mockImplementation((name: string) => {
        if (name === ChartCorePlugin.name) return chartCore;
        if (name === DataManagerPlugin.name) return dataManager;
        return null;
      })
    }
  });

  // Helper to create mock event bus wrapper
  const createMockEventBusWrapper = () => ({
    fire: vi.fn(),
    subscribe: vi.fn(),
    unsubscribe: vi.fn(),
    eventFactory: vi.fn().mockImplementation((fn) => fn()),
    destroy: vi.fn(),
  });

  type MockContext = ReturnType<typeof createMockContext>;
  type MockEventBusWrapper = ReturnType<typeof createMockEventBusWrapper>;

  let plugin: DataFeedPlugin;
  let mockDataFetch: IDataFetch;
  let mockContext: MockContext;
  let mockEventBusWrapper: MockEventBusWrapper;

  beforeEach(() => {
    mockDataFetch = createMockDataFetch();
    mockContext = createMockContext();
    mockEventBusWrapper = createMockEventBusWrapper();
    
    plugin = new DataFeedPlugin(mockDataFetch);
    // Mock the private properties
    (plugin as unknown as { _context: MockContext; _eventBus: MockEventBusWrapper })._context = mockContext;
    (plugin as unknown as { _context: MockContext; _eventBus: MockEventBusWrapper })._eventBus = mockEventBusWrapper;
  });

  describe('forward method', () => {
    it('should advance time correctly for different periods', () => {
      const baseTime = 1672531200 as Time; // 2023-01-01 00:00:00 UTC
      const baseDayjs = timeToDayjs(baseTime);
      
      // Mock data manager with different intervals
      const mockDataManager = createMockDataManager();
      mockContext.core.getPlugin.mockImplementation((name: string) => {
        if (name === DataManagerPlugin.name) return mockDataManager;
        return createMockChartCore();
      });

      // Test minute advancement
      mockDataManager.interval = { period: Period.minute, times: 1 };
      const minuteResult = plugin.forward(baseTime, 30);
      expect(minuteResult.unix()).toBe(baseDayjs.add(30, 'minute').unix());
      
      // Test hour advancement  
      mockDataManager.interval = { period: Period.hour, times: 1 };
      const hourResult = plugin.forward(baseTime, 2);
      expect(hourResult.unix()).toBe(baseDayjs.add(2, 'hour').unix());
      
      // Test day advancement
      mockDataManager.interval = { period: Period.day, times: 1 };
      const dayResult = plugin.forward(baseTime, 5);
      expect(dayResult.unix()).toBe(baseDayjs.add(5, 'day').unix());

      // Test week advancement
      mockDataManager.interval = { period: Period.week, times: 1 };
      const weekResult = plugin.forward(baseTime, 3);
      expect(weekResult.unix()).toBe(baseDayjs.add(3, 'week').unix());

      // Test month advancement
      mockDataManager.interval = { period: Period.month, times: 1 };
      const monthResult = plugin.forward(baseTime, 2);
      expect(monthResult.unix()).toBe(baseDayjs.add(2, 'month').unix());
    });

    it('should handle negative steps correctly', () => {
      const baseTime = 1672531200 as Time;
      const baseDayjs = timeToDayjs(baseTime);
      
      const result = plugin.forward(baseTime, -3);
      expect(result.unix()).toBe(baseDayjs.add(-3, 'day').unix());
    });

    it('should round fractional steps correctly', () => {
      const baseTime = 1672531200 as Time;
      const baseDayjs = timeToDayjs(baseTime);
      
      // 2.7 should round to 3
      const result = plugin.forward(baseTime, 2.7);
      expect(result.unix()).toBe(baseDayjs.add(3, 'day').unix());
    });
  });

  describe('loading state management', () => {
    it('should fire event when loading state changes', () => {
      plugin.loading = true;
      expect(mockEventBusWrapper.fire).toHaveBeenCalledWith({
        type: 'dataFeedLoading',
        payload: true
      });

      plugin.loading = false;
      expect(mockEventBusWrapper.fire).toHaveBeenCalledWith({
        type: 'dataFeedLoading',
        payload: false
      });
    });

    it('should return current loading state', () => {
      plugin.loading = true;
      expect(plugin.loading).toBe(true);

      plugin.loading = false;
      expect(plugin.loading).toBe(false);
    });
  });

  describe('setRange - initial data loading', () => {
    it('should fetch and set initial data correctly', async () => {
      const mockDataManager = createMockDataManager();
      mockContext.core.getPlugin.mockImplementation((name: string) => {
        if (name === DataManagerPlugin.name) return mockDataManager;
        return createMockChartCore();
      });

      await plugin.setRange(sampleRange);

      // Test core behavior: fetches data and sets it on data manager
      expect(mockDataFetch.fetchInitialData).toHaveBeenCalledWith(
        sampleRange,
        expect.objectContaining({
          forward: expect.any(Function)
        })
      );
      expect(mockDataManager.setMasterData).toHaveBeenCalledWith(sampleData, sampleRange.interval);
    });

    it('should handle loading state during data fetch', async () => {
      let loadingDuringFetch = false;
      mockDataFetch.fetchInitialData = vi.fn().mockImplementation(async () => {
        loadingDuringFetch = plugin.loading;
        return sampleData;
      });

      await plugin.setRange(sampleRange);

      // Should be loading during fetch
      expect(loadingDuringFetch).toBe(true);
      // Should not be loading after fetch completes
      expect(plugin.loading).toBe(false);
    });

    it('should fit chart range after data is loaded', async () => {
      const mockChartCore = createMockChartCore();
      mockContext.core.getPlugin.mockImplementation((name: string) => {
        if (name === ChartCorePlugin.name) return mockChartCore;
        return createMockDataManager();
      });

      await plugin.setRange(sampleRange);

      expect(mockChartCore.fitRange).toHaveBeenCalled();
    });
  });



  describe('plugin lifecycle', () => {
    it('should manage loading state correctly during setRange', async () => {
      // Should not be loading initially
      expect(plugin.loading).toBe(false);

      // Start setRange operation
      const setRangePromise = plugin.setRange(sampleRange);
      
      // Should complete successfully
      await setRangePromise;
      
      // Should not be loading after completion
      expect(plugin.loading).toBe(false);
    });
  });
}); 