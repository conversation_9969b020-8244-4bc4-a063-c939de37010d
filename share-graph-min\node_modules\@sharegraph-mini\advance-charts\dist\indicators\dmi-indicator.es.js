var z = Object.defineProperty;
var B = (a, i, e) => i in a ? z(a, i, { enumerable: !0, configurable: !0, writable: !0, value: e }) : a[i] = e;
var S = (a, i, e) => B(a, typeof i != "symbol" ? i + "" : i, e);
import { LineSeries as C } from "lightweight-charts";
import { ChartIndicator as F } from "./abstract-indicator.es.js";
import { RegionPrimitive as G } from "../custom-primitive/primitive/region.es.js";
import { autoScaleInfoProviderCreator as P } from "../helpers/utils.es.js";
const J = {
  adxColor: "#f23645",
  // Orange for ADX
  plusDIColor: "#2962ff",
  // Green for +DI
  minusDIColor: "#ff6d00",
  // Red for -DI
  priceLineColor: "rgba(150, 150, 150, 0.35)",
  backgroundColor: "#ff98001a",
  period: 14,
  overlay: !1
};
class $ extends F {
  constructor(e, s, o) {
    super(e, s);
    S(this, "adxSeries");
    S(this, "plusDISeries");
    S(this, "minusDISeries");
    this.adxSeries = e.addSeries(C, {
      color: this.options.adxColor,
      lineWidth: 2,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "dmi",
      autoscaleInfoProvider: P({ maxValue: 100, minValue: 0 })
    }, o), this.plusDISeries = e.addSeries(C, {
      color: this.options.plusDIColor,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "dmi",
      autoscaleInfoProvider: P({ maxValue: 100, minValue: 0 })
    }, o), this.minusDISeries = e.addSeries(C, {
      color: this.options.minusDIColor,
      lineWidth: 1,
      priceLineVisible: !1,
      crosshairMarkerVisible: !1,
      priceScaleId: "dmi",
      autoscaleInfoProvider: P({ maxValue: 100, minValue: 0 })
    }, o), this.adxSeries.attachPrimitive(
      new G({
        upPrice: 25,
        lowPrice: 20,
        lineColor: this.options.priceLineColor,
        backgroundColor: this.options.backgroundColor
      })
    );
  }
  getDefaultOptions() {
    return J;
  }
  formula(e) {
    const s = this.options.period, o = e.symbol.high, c = e.symbol.low, n = e.symbol.close, l = e.new_var(o, s + 1), w = e.new_var(c, s + 1), V = e.new_var(n, s + 1);
    if (!l.calculable() || !w.calculable() || !V.calculable())
      return;
    const f = o, I = c, W = l.get(1), H = w.get(1), y = V.get(1), u = f - W, p = H - I;
    let h = 0, d = 0;
    u > p && u > 0 && (h = u), p > u && p > 0 && (d = p);
    const X = f - I, j = Math.abs(f - y), q = Math.abs(I - y), M = Math.max(X, j, q), t = 2 / (s + 1), A = e.new_var(h, 2), _ = e.new_var(d, 2), E = e.new_var(M, 2), L = A.get(1), N = _.get(1), k = E.get(1);
    let m, D, r;
    isNaN(L) || isNaN(N) || isNaN(k) ? (m = h, D = d, r = M) : (m = t * h + (1 - t) * L, D = t * d + (1 - t) * N, r = t * M + (1 - t) * k), A.set(m), _.set(D), E.set(r);
    const x = r !== 0 ? 100 * m / r : 0, g = r !== 0 ? 100 * D / r : 0, O = x + g, b = O !== 0 ? 100 * Math.abs(x - g) / O : 0, T = e.new_var(b, 2), R = T.get(1);
    let v;
    return isNaN(R) ? v = b : v = t * b + (1 - t) * R, T.set(v), [
      v,
      x,
      g
    ];
  }
  applyIndicatorData() {
    const e = [], s = [], o = [];
    for (const c of this._executionContext.data) {
      const n = c.value;
      if (!n) continue;
      const l = c.time;
      e.push({ time: l, value: n[0] }), s.push({ time: l, value: n[1] }), o.push({ time: l, value: n[2] });
    }
    this.adxSeries.setData(e), this.plusDISeries.setData(s), this.minusDISeries.setData(o);
  }
  remove() {
    super.remove(), this.chart.removeSeries(this.adxSeries), this.chart.removeSeries(this.plusDISeries), this.chart.removeSeries(this.minusDISeries);
  }
  _applyOptions() {
    this.adxSeries.applyOptions({ color: this.options.adxColor }), this.plusDISeries.applyOptions({ color: this.options.plusDIColor }), this.minusDISeries.applyOptions({ color: this.options.minusDIColor }), this.applyIndicatorData();
  }
  setPaneIndex(e) {
    this.adxSeries.moveToPane(e), this.plusDISeries.moveToPane(e), this.minusDISeries.moveToPane(e);
  }
  getPaneIndex() {
    return this.adxSeries.getPane().paneIndex();
  }
}
export {
  $ as default,
  J as defaultOptions
};
//# sourceMappingURL=dmi-indicator.es.js.map
