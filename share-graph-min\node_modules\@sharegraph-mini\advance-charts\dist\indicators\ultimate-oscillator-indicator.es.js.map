{"version": 3, "file": "ultimate-oscillator-indicator.es.js", "sources": ["../../src/indicators/ultimate-oscillator-indicator.ts"], "sourcesContent": ["import {IChartApi, ISeriesApi, LineSeries, Nominal, SeriesType, SingleValueData, Time} from \"lightweight-charts\";\nimport {ChartIndicator, ChartIndicatorOptions} from \"./abstract-indicator\";\nimport {RegionPrimitive} from \"../custom-primitive/primitive/region\";\nimport {autoScaleInfoProviderCreator} from \"../helpers/utils\";\nimport {Context} from \"../helpers/execution-indicator\";\n\nexport interface UltimateOscillatorIndicatorOptions extends ChartIndicatorOptions {\n  color: string,\n  period1: number,\n  period2: number,\n  period3: number,\n  weight1: number,\n  weight2: number,\n  weight3: number,\n  priceLineColor: string,\n  backgroundColor: string\n}\n\nexport const defaultOptions: UltimateOscillatorIndicatorOptions = {\n  color: \"#3179f5\",     // for Ultimate Oscillator\n  priceLineColor: \"#a1c3ff\",\n  backgroundColor: '#d2e0fa',\n  period1: 7,      // Short period as per TradingView standard\n  period2: 14,     // Medium period as per TradingView standard\n  period3: 28,     // Long period as per TradingView standard\n  weight1: 4,      // Weight for short period\n  weight2: 2,      // Weight for medium period\n  weight3: 1,      // Weight for long period\n  overlay: false\n}\n\nexport type UltimateOscillatorLine = Nominal<number, 'UltimateOscillator'>\n\nexport type UltimateOscillatorData = [UltimateOscillatorLine]\n\nexport default class UltimateOscillatorIndicator extends ChartIndicator<UltimateOscillatorIndicatorOptions, UltimateOscillatorData> {\n  ultimateOscillatorSeries: ISeriesApi<SeriesType>\n\n  constructor(chart: IChartApi, options?: Partial<UltimateOscillatorIndicatorOptions>, paneIndex?: number) {\n    super(chart, options)\n    \n    this.ultimateOscillatorSeries = chart.addSeries(LineSeries, {\n      color: this.options.color,\n      lineWidth: 2,\n      priceLineVisible: false,\n      crosshairMarkerVisible: false,\n      priceScaleId: 'ultimateoscillator',\n      autoscaleInfoProvider: autoScaleInfoProviderCreator({maxValue: 100, minValue: 0})\n    }, paneIndex);\n    \n    // Add region primitives for visual reference at 30 and 70 thresholds\n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 75,\n        lowPrice: 70,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n    \n    this.ultimateOscillatorSeries.attachPrimitive(\n      new RegionPrimitive({\n        upPrice: 30,\n        lowPrice: 25,\n        lineColor: this.options.priceLineColor,\n        backgroundColor: this.options.backgroundColor\n      })\n    );\n  }\n\n  getDefaultOptions(): UltimateOscillatorIndicatorOptions {\n    return defaultOptions\n  }\n\n  formula(c: Context): UltimateOscillatorData | undefined {\n    const period1 = this.options.period1;\n    const period2 = this.options.period2;\n    const period3 = this.options.period3;\n    const weight1 = this.options.weight1;\n    const weight2 = this.options.weight2;\n    const weight3 = this.options.weight3;\n\n    const high = c.symbol.high;\n    const low = c.symbol.low;\n    const close = c.symbol.close;\n\n    // We need enough data for the longest period calculation plus one for previous close\n    const maxPeriod = Math.max(period1, period2, period3);\n\n    // Get previous close for True Range and Buying Pressure calculations\n    const closeSeries = c.new_var(close, 2);\n    if (!closeSeries.calculable()) {\n      return;\n    }\n    const prevClose = closeSeries.get(1);\n\n    // Step 1: Calculate Buying Pressure (BP) and True Range (TR)\n    // Following exact Larry Williams / TradingView Ultimate Oscillator formula:\n    // BP = Close - Minimum(Low, Previous Close)\n    // TR = Maximum(High, Previous Close) - Minimum(Low, Previous Close)\n\n    // Buying Pressure calculation\n    const minLowPrevClose = Math.min(low, prevClose);\n    const buyingPressure = close - minLowPrevClose;\n\n    // True Range calculation - using Ultimate Oscillator specific formula\n    const maxHighPrevClose = Math.max(high, prevClose);\n    const trueRange = maxHighPrevClose - minLowPrevClose;\n\n    // Step 2: Maintain history for BP and TR calculations\n    // Store BP and TR values for period calculations\n    const bpSeries = c.new_var(buyingPressure, maxPeriod);\n    const trSeries = c.new_var(trueRange, maxPeriod);\n\n    if (!bpSeries.calculable() || !trSeries.calculable()) {\n      return;\n    }\n\n    const bpValues = bpSeries.getAll();\n    const trValues = trSeries.getAll();\n\n    // Ensure we have enough data for the longest period\n    if (bpValues.length < maxPeriod || trValues.length < maxPeriod) {\n      return;\n    }\n\n    // Step 3: Calculate averages for each period\n    // Average7 = (7-period BP Sum) / (7-period TR Sum)\n    // Average14 = (14-period BP Sum) / (14-period TR Sum)\n    // Average28 = (28-period BP Sum) / (28-period TR Sum)\n\n    const bp1Sum = bpValues.slice(-period1).reduce((sum, val) => sum + val, 0);\n    const tr1Sum = trValues.slice(-period1).reduce((sum, val) => sum + val, 0);\n    const average1 = tr1Sum !== 0 ? bp1Sum / tr1Sum : 0;\n\n    const bp2Sum = bpValues.slice(-period2).reduce((sum, val) => sum + val, 0);\n    const tr2Sum = trValues.slice(-period2).reduce((sum, val) => sum + val, 0);\n    const average2 = tr2Sum !== 0 ? bp2Sum / tr2Sum : 0;\n\n    const bp3Sum = bpValues.slice(-period3).reduce((sum, val) => sum + val, 0);\n    const tr3Sum = trValues.slice(-period3).reduce((sum, val) => sum + val, 0);\n    const average3 = tr3Sum !== 0 ? bp3Sum / tr3Sum : 0;\n\n    // Step 4: Calculate Ultimate Oscillator using exact Larry Williams formula\n    // UO = 100 × [(4×Average7) + (2×Average14) + Average28] / (4+2+1)\n    const weightedSum = (weight1 * average1) + (weight2 * average2) + (weight3 * average3);\n    const totalWeight = weight1 + weight2 + weight3;\n    const ultimateOscillator = totalWeight !== 0 ? (100 * weightedSum) / totalWeight : 0;\n\n    return [ultimateOscillator as UltimateOscillatorLine];\n  }\n\n\n  applyIndicatorData() {\n    const ultimateOscillatorData: SingleValueData[] = [];\n    \n    for(const bar of this._executionContext.data) {\n      const value = bar.value;\n      if(!value) continue;\n      \n      const time = bar.time as Time;\n      ultimateOscillatorData.push({time, value: value[0]});\n    }\n\n    this.ultimateOscillatorSeries.setData(ultimateOscillatorData);\n  }\n\n  remove() {\n    super.remove()\n    this.chart.removeSeries(this.ultimateOscillatorSeries);\n  }\n\n  _applyOptions() {\n    this.ultimateOscillatorSeries.applyOptions({color: this.options.color});\n    this.applyIndicatorData();\n  }\n\n  setPaneIndex(paneIndex: number) {\n    this.ultimateOscillatorSeries.moveToPane(paneIndex);\n  }\n\n  getPaneIndex(): number {\n    return this.ultimateOscillatorSeries.getPane().paneIndex();\n  }\n}\n"], "names": ["defaultOptions", "UltimateOscillatorIndicator", "ChartIndicator", "chart", "options", "paneIndex", "__publicField", "LineSeries", "autoScaleInfoProviderCreator", "RegionPrimitive", "c", "period1", "period2", "period3", "weight1", "weight2", "weight3", "high", "low", "close", "max<PERSON><PERSON><PERSON>", "closeSeries", "prevClose", "minLowPrevClose", "buyingPressure", "trueRang<PERSON>", "bpSeries", "trSeries", "bpV<PERSON>ues", "tr<PERSON><PERSON><PERSON>", "bp1Sum", "sum", "val", "tr1Sum", "average1", "bp2Sum", "tr2Sum", "average2", "bp3Sum", "tr3Sum", "average3", "weightedSum", "totalWeight", "ultimateOscillatorData", "bar", "value", "time"], "mappings": ";;;;;;;AAkBO,MAAMA,IAAqD;AAAA,EAChE,OAAO;AAAA;AAAA,EACP,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,SAAS;AACX;AAMA,MAAqBC,UAAoCC,EAA2E;AAAA,EAGlI,YAAYC,GAAkBC,GAAuDC,GAAoB;AACvG,UAAMF,GAAOC,CAAO;AAHtB,IAAAE,EAAA;AAKO,SAAA,2BAA2BH,EAAM,UAAUI,GAAY;AAAA,MAC1D,OAAO,KAAK,QAAQ;AAAA,MACpB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,uBAAuBC,EAA6B,EAAC,UAAU,KAAK,UAAU,EAAE,CAAA;AAAA,OAC/EH,CAAS,GAGZ,KAAK,yBAAyB;AAAA,MAC5B,IAAII,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH,GAEA,KAAK,yBAAyB;AAAA,MAC5B,IAAIA,EAAgB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW,KAAK,QAAQ;AAAA,QACxB,iBAAiB,KAAK,QAAQ;AAAA,MAC/B,CAAA;AAAA,IACH;AAAA,EAAA;AAAA,EAGF,oBAAwD;AAC/C,WAAAT;AAAA,EAAA;AAAA,EAGT,QAAQU,GAAgD;AAChD,UAAAC,IAAU,KAAK,QAAQ,SACvBC,IAAU,KAAK,QAAQ,SACvBC,IAAU,KAAK,QAAQ,SACvBC,IAAU,KAAK,QAAQ,SACvBC,IAAU,KAAK,QAAQ,SACvBC,IAAU,KAAK,QAAQ,SAEvBC,IAAOP,EAAE,OAAO,MAChBQ,IAAMR,EAAE,OAAO,KACfS,IAAQT,EAAE,OAAO,OAGjBU,IAAY,KAAK,IAAIT,GAASC,GAASC,CAAO,GAG9CQ,IAAcX,EAAE,QAAQS,GAAO,CAAC;AAClC,QAAA,CAACE,EAAY;AACf;AAEI,UAAAC,IAAYD,EAAY,IAAI,CAAC,GAQ7BE,IAAkB,KAAK,IAAIL,GAAKI,CAAS,GACzCE,IAAiBL,IAAQI,GAIzBE,IADmB,KAAK,IAAIR,GAAMK,CAAS,IACZC,GAI/BG,IAAWhB,EAAE,QAAQc,GAAgBJ,CAAS,GAC9CO,IAAWjB,EAAE,QAAQe,GAAWL,CAAS;AAE/C,QAAI,CAACM,EAAS,WAAA,KAAgB,CAACC,EAAS;AACtC;AAGI,UAAAC,IAAWF,EAAS,OAAO,GAC3BG,IAAWF,EAAS,OAAO;AAGjC,QAAIC,EAAS,SAASR,KAAaS,EAAS,SAAST;AACnD;AAQF,UAAMU,IAASF,EAAS,MAAM,CAACjB,CAAO,EAAE,OAAO,CAACoB,GAAKC,MAAQD,IAAMC,GAAK,CAAC,GACnEC,IAASJ,EAAS,MAAM,CAAClB,CAAO,EAAE,OAAO,CAACoB,GAAKC,MAAQD,IAAMC,GAAK,CAAC,GACnEE,IAAWD,MAAW,IAAIH,IAASG,IAAS,GAE5CE,IAASP,EAAS,MAAM,CAAChB,CAAO,EAAE,OAAO,CAACmB,GAAKC,MAAQD,IAAMC,GAAK,CAAC,GACnEI,IAASP,EAAS,MAAM,CAACjB,CAAO,EAAE,OAAO,CAACmB,GAAKC,MAAQD,IAAMC,GAAK,CAAC,GACnEK,IAAWD,MAAW,IAAID,IAASC,IAAS,GAE5CE,IAASV,EAAS,MAAM,CAACf,CAAO,EAAE,OAAO,CAACkB,GAAKC,MAAQD,IAAMC,GAAK,CAAC,GACnEO,IAASV,EAAS,MAAM,CAAChB,CAAO,EAAE,OAAO,CAACkB,GAAKC,MAAQD,IAAMC,GAAK,CAAC,GACnEQ,IAAWD,MAAW,IAAID,IAASC,IAAS,GAI5CE,IAAe3B,IAAUoB,IAAanB,IAAUsB,IAAarB,IAAUwB,GACvEE,IAAc5B,IAAUC,IAAUC;AAGxC,WAAO,CAFoB0B,MAAgB,IAAK,MAAMD,IAAeC,IAAc,CAE/B;AAAA,EAAA;AAAA,EAItD,qBAAqB;AACnB,UAAMC,IAA4C,CAAC;AAEzC,eAAAC,KAAO,KAAK,kBAAkB,MAAM;AAC5C,YAAMC,IAAQD,EAAI;AAClB,UAAG,CAACC,EAAO;AAEX,YAAMC,IAAOF,EAAI;AACjB,MAAAD,EAAuB,KAAK,EAAC,MAAAG,GAAM,OAAOD,EAAM,CAAC,GAAE;AAAA,IAAA;AAGhD,SAAA,yBAAyB,QAAQF,CAAsB;AAAA,EAAA;AAAA,EAG9D,SAAS;AACP,UAAM,OAAO,GACR,KAAA,MAAM,aAAa,KAAK,wBAAwB;AAAA,EAAA;AAAA,EAGvD,gBAAgB;AACd,SAAK,yBAAyB,aAAa,EAAC,OAAO,KAAK,QAAQ,OAAM,GACtE,KAAK,mBAAmB;AAAA,EAAA;AAAA,EAG1B,aAAatC,GAAmB;AACzB,SAAA,yBAAyB,WAAWA,CAAS;AAAA,EAAA;AAAA,EAGpD,eAAuB;AACrB,WAAO,KAAK,yBAAyB,QAAQ,EAAE,UAAU;AAAA,EAAA;AAE7D;"}