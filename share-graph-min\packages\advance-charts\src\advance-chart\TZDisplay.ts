import { Delegate } from '../helpers/delegate';
import { Interval, Period } from './i-advance-chart';
import {TickMarkType} from 'lightweight-charts';
import dayjs from '../helpers/dayjs-setup'
import {merge} from 'es-toolkit';

export interface ITZDisplayOptions {
  tzDisplay: string;
  locale: string;
  dataInterval: Interval;
}

export class TZDisplay {
  _browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  _optionsChanged = new Delegate<Partial<ITZDisplayOptions>>();

  constructor(
    private options: ITZDisplayOptions = {
      tzDisplay: Intl.DateTimeFormat().resolvedOptions().timeZone,
      locale: 'en-US',
      dataInterval: { period: Period.day, times: 1 },
    },
  ) {}

  applyOptions(options: Partial<ITZDisplayOptions>) {
    this.options = merge(this.options, options);
    this._optionsChanged.fire(options);
  }

  formatDateTime(date: Date) {
    date = this.convertDateToTimezoneDate(date);
    return new Intl.DateTimeFormat(this.options.locale, {
      dateStyle: 'medium',
      timeStyle: 'medium',
      hourCycle: 'h23', 
    }).format(date);
  }
  formatDate(date: Date) {
    date = this.convertDateToTimezoneDate(date);
    return new Intl.DateTimeFormat(this.options.locale, {
      dateStyle: 'medium',
    }).format(date);
  }

  convertDateToTimezoneDate(date: Date) {
    if(this.options.tzDisplay === this._browserTimezone) return date;
    return dayjs.tz(date, this.options.tzDisplay).tz(this._browserTimezone, true).toDate()
  }

  format(date: Date) {
    switch(this.options.dataInterval.period) {
      case Period.day:
      case Period.month:
      case Period.week:
        return this.formatDate(date);
      default:
        return this.formatDateTime(date)
    }
  }

  tickMarkFormatter(date: Date, tickMarkType: TickMarkType) {
    date = this.convertDateToTimezoneDate(date);
    const formatOptions: Intl.DateTimeFormatOptions = {};

    switch (tickMarkType) {
      case TickMarkType.Year:
        formatOptions.year = 'numeric';
        break;

      case TickMarkType.Month:
        formatOptions.month = 'short';
        break;

      case TickMarkType.DayOfMonth:
        formatOptions.day = 'numeric';
        break;

      case TickMarkType.Time:
        formatOptions.hour12 = false;
        formatOptions.hour = '2-digit';
        formatOptions.minute = '2-digit';
        break;

      case TickMarkType.TimeWithSeconds:
        formatOptions.hour12 = false;
        formatOptions.hour = '2-digit';
        formatOptions.minute = '2-digit';
        formatOptions.second = '2-digit';
        break;
    }

    return date.toLocaleString(this.options.locale, formatOptions)
  }
}
